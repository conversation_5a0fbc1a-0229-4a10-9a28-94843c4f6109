# 🌐 PublicAgent - Marketing Website

## Vai trò
Bạn là **PublicAgent** chuyên xây dựng public website để marketing và bán dịch vụ SaaS hiệu thuốc.

## Nhiệm vụ chính
- Tạo landing page với <PERSON> + Tailwind CSS
- Pricing plans và subscription management
- Tenant registration và onboarding
- SEO optimization và performance
- Contact forms và lead generation
- Blog/news system

## Public Website Structure

### 1. Routes Structure
```php
// routes/web.php (Central domain)
Route::middleware(['web'])->group(function () {
    // Homepage
    Route::get('/', [HomeController::class, 'index'])->name('home');
    
    // About & Features
    Route::get('/about', [PageController::class, 'about'])->name('about');
    Route::get('/features', [PageController::class, 'features'])->name('features');
    Route::get('/pricing', [PageController::class, 'pricing'])->name('pricing');
    
    // Registration & Onboarding
    Route::get('/signup', [TenantController::class, 'showSignup'])->name('signup');
    Route::post('/signup', [TenantController::class, 'createTenant'])->name('signup.store');
    Route::get('/signup/success', [TenantController::class, 'signupSuccess'])->name('signup.success');
    
    // Contact & Support
    Route::get('/contact', [ContactController::class, 'show'])->name('contact');
    Route::post('/contact', [ContactController::class, 'store'])->name('contact.store');
    Route::get('/support', [SupportController::class, 'index'])->name('support');
    
    // Blog
    Route::get('/blog', [BlogController::class, 'index'])->name('blog.index');
    Route::get('/blog/{slug}', [BlogController::class, 'show'])->name('blog.show');
    
    // Legal
    Route::get('/terms', [PageController::class, 'terms'])->name('terms');
    Route::get('/privacy', [PageController::class, 'privacy'])->name('privacy');
    
    // Demo
    Route::get('/demo', [DemoController::class, 'show'])->name('demo');
    Route::post('/demo/request', [DemoController::class, 'request'])->name('demo.request');
});
```

### 2. Controllers

#### Home Controller
```php
// app/Http/Controllers/HomeController.php
class HomeController extends Controller
{
    public function index()
    {
        $stats = [
            'total_pharmacies' => Tenant::where('subscription_status', 'active')->count(),
            'total_invoices' => $this->getTotalInvoicesAcrossAllTenants(),
            'total_revenue' => $this->getTotalRevenueAcrossAllTenants(),
            'customer_satisfaction' => 98.5, // Static for now
        ];
        
        $testimonials = [
            [
                'name' => 'Nguyễn Văn A',
                'pharmacy' => 'Hiệu thuốc An Khang',
                'content' => 'Hệ thống giúp chúng tôi quản lý hiệu thuốc hiệu quả hơn 300%',
                'rating' => 5,
                'avatar' => '/images/testimonials/nguyen-van-a.jpg'
            ],
            [
                'name' => 'Trần Thị B',
                'pharmacy' => 'Nhà thuốc Sức Khỏe',
                'content' => 'Tính năng tích điểm khách hàng rất tuyệt vời',
                'rating' => 5,
                'avatar' => '/images/testimonials/tran-thi-b.jpg'
            ],
        ];
        
        $features = [
            [
                'icon' => 'heroicon-o-beaker',
                'title' => 'Quản lý thuốc thông minh',
                'description' => 'Theo dõi tồn kho, hạn sử dụng và FEFO tự động'
            ],
            [
                'icon' => 'heroicon-o-document-text',
                'title' => 'Hóa đơn điện tử',
                'description' => 'Xuất hóa đơn điện tử tuân thủ quy định thuế'
            ],
            [
                'icon' => 'heroicon-o-users',
                'title' => 'Quản lý khách hàng',
                'description' => 'Tích điểm, phân loại và chăm sóc khách hàng'
            ],
            [
                'icon' => 'heroicon-o-chart-bar',
                'title' => 'Báo cáo chi tiết',
                'description' => 'Phân tích doanh thu, tồn kho và hiệu quả kinh doanh'
            ],
        ];
        
        return view('public.home', compact('stats', 'testimonials', 'features'));
    }
    
    private function getTotalInvoicesAcrossAllTenants(): int
    {
        $total = 0;
        
        Tenant::chunk(100, function ($tenants) use (&$total) {
            foreach ($tenants as $tenant) {
                $tenant->run(function () use (&$total) {
                    $total += Invoice::count();
                });
            }
        });
        
        return $total;
    }
}
```

#### Tenant Controller
```php
// app/Http/Controllers/TenantController.php
class TenantController extends Controller
{
    public function showSignup()
    {
        $plans = [
            'trial' => [
                'name' => 'Dùng thử miễn phí',
                'price' => 0,
                'duration' => '14 ngày',
                'features' => [
                    'Quản lý tối đa 100 thuốc',
                    'Tối đa 50 hóa đơn/tháng',
                    'Hỗ trợ email',
                    'Báo cáo cơ bản'
                ]
            ],
            'basic' => [
                'name' => 'Gói cơ bản',
                'price' => 299000,
                'duration' => 'tháng',
                'features' => [
                    'Quản lý không giới hạn thuốc',
                    'Không giới hạn hóa đơn',
                    'Hỗ trợ điện thoại',
                    'Báo cáo chi tiết',
                    'Tích điểm khách hàng'
                ]
            ],
            'premium' => [
                'name' => 'Gói cao cấp',
                'price' => 599000,
                'duration' => 'tháng',
                'features' => [
                    'Tất cả tính năng gói cơ bản',
                    'Ứng dụng mobile',
                    'API tích hợp',
                    'Hỗ trợ 24/7',
                    'Backup tự động',
                    'Multi-location'
                ]
            ]
        ];
        
        return view('public.signup', compact('plans'));
    }
    
    public function createTenant(SignupRequest $request)
    {
        try {
            DB::beginTransaction();
            
            $tenantData = $request->validated();
            
            // Create tenant
            $tenant = app(TenantService::class)->createTenant($tenantData);
            
            // Send welcome email
            Mail::to($tenantData['owner_email'])
                ->send(new WelcomeEmail($tenant, $tenantData['password']));
            
            // Send notification to admin
            event(new NewTenantRegistered($tenant));
            
            DB::commit();
            
            return redirect()->route('signup.success')
                ->with('tenant', $tenant);
                
        } catch (\Exception $e) {
            DB::rollback();
            
            return back()
                ->withInput()
                ->withErrors(['general' => 'Có lỗi xảy ra. Vui lòng thử lại.']);
        }
    }
    
    public function signupSuccess()
    {
        $tenant = session('tenant');
        
        if (!$tenant) {
            return redirect()->route('home');
        }
        
        return view('public.signup-success', compact('tenant'));
    }
}
```

### 3. Views (Blade Templates)

#### Homepage Layout
```blade
{{-- resources/views/layouts/public.blade.php --}}
<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>@yield('title', 'PharmaSaaS - Hệ thống quản lý hiệu thuốc')</title>
    <meta name="description" content="@yield('description', 'Giải pháp SaaS quản lý hiệu thuốc toàn diện với hóa đơn điện tử, quản lý tồn kho và khách hàng')">
    
    <!-- SEO Meta Tags -->
    <meta property="og:title" content="@yield('og_title', 'PharmaSaaS - Hệ thống quản lý hiệu thuốc')">
    <meta property="og:description" content="@yield('og_description', 'Giải pháp SaaS quản lý hiệu thuốc toàn diện')">
    <meta property="og:image" content="@yield('og_image', asset('images/og-image.jpg'))">
    <meta property="og:url" content="{{ url()->current() }}">
    
    <!-- Tailwind CSS -->
    @vite(['resources/css/app.css', 'resources/js/app.js'])
    
    <!-- Google Analytics -->
    @if(config('services.google_analytics.id'))
    <script async src="https://www.googletagmanager.com/gtag/js?id={{ config('services.google_analytics.id') }}"></script>
    <script>
        window.dataLayer = window.dataLayer || [];
        function gtag(){dataLayer.push(arguments);}
        gtag('js', new Date());
        gtag('config', '{{ config('services.google_analytics.id') }}');
    </script>
    @endif
</head>
<body class="bg-gray-50">
    <!-- Navigation -->
    <nav class="bg-white shadow-lg fixed w-full z-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-16">
                <div class="flex items-center">
                    <a href="{{ route('home') }}" class="flex-shrink-0">
                        <img class="h-8 w-auto" src="{{ asset('images/logo.svg') }}" alt="PharmaSaaS">
                    </a>
                </div>
                
                <div class="hidden md:flex items-center space-x-8">
                    <a href="{{ route('features') }}" class="text-gray-700 hover:text-blue-600">Tính năng</a>
                    <a href="{{ route('pricing') }}" class="text-gray-700 hover:text-blue-600">Bảng giá</a>
                    <a href="{{ route('blog.index') }}" class="text-gray-700 hover:text-blue-600">Blog</a>
                    <a href="{{ route('contact') }}" class="text-gray-700 hover:text-blue-600">Liên hệ</a>
                    <a href="{{ route('demo') }}" class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700">Dùng thử</a>
                </div>
                
                <!-- Mobile menu button -->
                <div class="md:hidden flex items-center">
                    <button type="button" class="mobile-menu-button">
                        <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16" />
                        </svg>
                    </button>
                </div>
            </div>
        </div>
        
        <!-- Mobile menu -->
        <div class="mobile-menu hidden md:hidden">
            <div class="px-2 pt-2 pb-3 space-y-1 sm:px-3 bg-white border-t">
                <a href="{{ route('features') }}" class="block px-3 py-2 text-gray-700">Tính năng</a>
                <a href="{{ route('pricing') }}" class="block px-3 py-2 text-gray-700">Bảng giá</a>
                <a href="{{ route('blog.index') }}" class="block px-3 py-2 text-gray-700">Blog</a>
                <a href="{{ route('contact') }}" class="block px-3 py-2 text-gray-700">Liên hệ</a>
                <a href="{{ route('demo') }}" class="block px-3 py-2 bg-blue-600 text-white rounded-md">Dùng thử</a>
            </div>
        </div>
    </nav>
    
    <!-- Main Content -->
    <main class="pt-16">
        @yield('content')
    </main>
    
    <!-- Footer -->
    <footer class="bg-gray-900 text-white">
        <div class="max-w-7xl mx-auto py-12 px-4 sm:px-6 lg:px-8">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-8">
                <div>
                    <img class="h-8 w-auto mb-4" src="{{ asset('images/logo-white.svg') }}" alt="PharmaSaaS">
                    <p class="text-gray-300">Giải pháp SaaS quản lý hiệu thuốc toàn diện</p>
                </div>
                
                <div>
                    <h3 class="text-lg font-semibold mb-4">Sản phẩm</h3>
                    <ul class="space-y-2">
                        <li><a href="{{ route('features') }}" class="text-gray-300 hover:text-white">Tính năng</a></li>
                        <li><a href="{{ route('pricing') }}" class="text-gray-300 hover:text-white">Bảng giá</a></li>
                        <li><a href="{{ route('demo') }}" class="text-gray-300 hover:text-white">Demo</a></li>
                    </ul>
                </div>
                
                <div>
                    <h3 class="text-lg font-semibold mb-4">Hỗ trợ</h3>
                    <ul class="space-y-2">
                        <li><a href="{{ route('support') }}" class="text-gray-300 hover:text-white">Trung tâm hỗ trợ</a></li>
                        <li><a href="{{ route('contact') }}" class="text-gray-300 hover:text-white">Liên hệ</a></li>
                        <li><a href="{{ route('blog.index') }}" class="text-gray-300 hover:text-white">Blog</a></li>
                    </ul>
                </div>
                
                <div>
                    <h3 class="text-lg font-semibold mb-4">Pháp lý</h3>
                    <ul class="space-y-2">
                        <li><a href="{{ route('terms') }}" class="text-gray-300 hover:text-white">Điều khoản</a></li>
                        <li><a href="{{ route('privacy') }}" class="text-gray-300 hover:text-white">Bảo mật</a></li>
                    </ul>
                </div>
            </div>
            
            <div class="mt-8 pt-8 border-t border-gray-700 text-center">
                <p class="text-gray-300">&copy; {{ date('Y') }} PharmaSaaS. All rights reserved.</p>
            </div>
        </div>
    </footer>
    
    <script>
        // Mobile menu toggle
        document.querySelector('.mobile-menu-button').addEventListener('click', function() {
            document.querySelector('.mobile-menu').classList.toggle('hidden');
        });
    </script>
</body>
</html>
```

#### Homepage Content
```blade
{{-- resources/views/public/home.blade.php --}}
@extends('layouts.public')

@section('title', 'PharmaSaaS - Hệ thống quản lý hiệu thuốc hàng đầu Việt Nam')
@section('description', 'Giải pháp SaaS quản lý hiệu thuốc toàn diện với hóa đơn điện tử, quản lý tồn kho, khách hàng và báo cáo chi tiết. Dùng thử miễn phí 14 ngày.')

@section('content')
<!-- Hero Section -->
<section class="bg-gradient-to-r from-blue-600 to-blue-800 text-white">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-20">
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <div>
                <h1 class="text-4xl lg:text-6xl font-bold mb-6">
                    Quản lý hiệu thuốc
                    <span class="text-blue-200">thông minh</span>
                </h1>
                <p class="text-xl mb-8 text-blue-100">
                    Giải pháp SaaS toàn diện giúp hiệu thuốc quản lý tồn kho, bán hàng, 
                    khách hàng và tuân thủ quy định thuế một cách hiệu quả.
                </p>
                <div class="flex flex-col sm:flex-row gap-4">
                    <a href="{{ route('signup') }}" 
                       class="bg-white text-blue-600 px-8 py-3 rounded-lg font-semibold hover:bg-gray-100 text-center">
                        Dùng thử miễn phí 14 ngày
                    </a>
                    <a href="{{ route('demo') }}" 
                       class="border-2 border-white text-white px-8 py-3 rounded-lg font-semibold hover:bg-white hover:text-blue-600 text-center">
                        Xem demo
                    </a>
                </div>
            </div>
            
            <div class="relative">
                <img src="{{ asset('images/dashboard-preview.png') }}" 
                     alt="PharmaSaaS Dashboard" 
                     class="rounded-lg shadow-2xl">
                <div class="absolute -bottom-4 -left-4 bg-white p-4 rounded-lg shadow-lg">
                    <div class="flex items-center space-x-2">
                        <div class="w-3 h-3 bg-green-500 rounded-full"></div>
                        <span class="text-gray-700 font-medium">{{ number_format($stats['total_pharmacies']) }}+ hiệu thuốc tin dùng</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Stats Section -->
<section class="py-16 bg-white">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="grid grid-cols-2 lg:grid-cols-4 gap-8">
            <div class="text-center">
                <div class="text-3xl lg:text-4xl font-bold text-blue-600 mb-2">{{ number_format($stats['total_pharmacies']) }}+</div>
                <div class="text-gray-600">Hiệu thuốc</div>
            </div>
            <div class="text-center">
                <div class="text-3xl lg:text-4xl font-bold text-blue-600 mb-2">{{ number_format($stats['total_invoices']) }}+</div>
                <div class="text-gray-600">Hóa đơn</div>
            </div>
            <div class="text-center">
                <div class="text-3xl lg:text-4xl font-bold text-blue-600 mb-2">₫{{ number_format($stats['total_revenue'] / 1000000) }}M+</div>
                <div class="text-gray-600">Doanh thu</div>
            </div>
            <div class="text-center">
                <div class="text-3xl lg:text-4xl font-bold text-blue-600 mb-2">{{ $stats['customer_satisfaction'] }}%</div>
                <div class="text-gray-600">Hài lòng</div>
            </div>
        </div>
    </div>
</section>

<!-- Features Section -->
<section class="py-20 bg-gray-50">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-16">
            <h2 class="text-3xl lg:text-4xl font-bold text-gray-900 mb-4">
                Tính năng nổi bật
            </h2>
            <p class="text-xl text-gray-600 max-w-3xl mx-auto">
                Tất cả những gì bạn cần để quản lý hiệu thuốc hiệu quả và tuân thủ quy định
            </p>
        </div>
        
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            @foreach($features as $feature)
            <div class="bg-white p-6 rounded-lg shadow-md hover:shadow-lg transition-shadow">
                <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mb-4">
                    <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <!-- Icon will be rendered based on $feature['icon'] -->
                    </svg>
                </div>
                <h3 class="text-lg font-semibold text-gray-900 mb-2">{{ $feature['title'] }}</h3>
                <p class="text-gray-600">{{ $feature['description'] }}</p>
            </div>
            @endforeach
        </div>
    </div>
</section>

<!-- Testimonials Section -->
<section class="py-20 bg-white">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-16">
            <h2 class="text-3xl lg:text-4xl font-bold text-gray-900 mb-4">
                Khách hàng nói gì về chúng tôi
            </h2>
        </div>
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
            @foreach($testimonials as $testimonial)
            <div class="bg-gray-50 p-8 rounded-lg">
                <div class="flex items-center mb-4">
                    @for($i = 1; $i <= 5; $i++)
                        <svg class="w-5 h-5 {{ $i <= $testimonial['rating'] ? 'text-yellow-400' : 'text-gray-300' }}" 
                             fill="currentColor" viewBox="0 0 20 20">
                            <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/>
                        </svg>
                    @endfor
                </div>
                <p class="text-gray-700 mb-6 italic">"{{ $testimonial['content'] }}"</p>
                <div class="flex items-center">
                    <img class="w-12 h-12 rounded-full mr-4" src="{{ $testimonial['avatar'] }}" alt="{{ $testimonial['name'] }}">
                    <div>
                        <div class="font-semibold text-gray-900">{{ $testimonial['name'] }}</div>
                        <div class="text-gray-600">{{ $testimonial['pharmacy'] }}</div>
                    </div>
                </div>
            </div>
            @endforeach
        </div>
    </div>
</section>

<!-- CTA Section -->
<section class="py-20 bg-blue-600">
    <div class="max-w-4xl mx-auto text-center px-4 sm:px-6 lg:px-8">
        <h2 class="text-3xl lg:text-4xl font-bold text-white mb-4">
            Sẵn sàng bắt đầu?
        </h2>
        <p class="text-xl text-blue-100 mb-8">
            Tham gia cùng hàng nghìn hiệu thuốc đã tin dùng PharmaSaaS
        </p>
        <a href="{{ route('signup') }}" 
           class="bg-white text-blue-600 px-8 py-4 rounded-lg font-semibold text-lg hover:bg-gray-100 inline-block">
            Bắt đầu dùng thử miễn phí
        </a>
        <p class="text-blue-100 mt-4">Không cần thẻ tín dụng • Hủy bất cứ lúc nào</p>
    </div>
</section>
@endsection
```

### 4. SEO & Performance

#### SEO Service
```php
// app/Services/SeoService.php
class SeoService
{
    public function generateSitemap()
    {
        $sitemap = app('sitemap');
        
        // Add static pages
        $sitemap->add(route('home'), now(), '1.0', 'daily');
        $sitemap->add(route('features'), now(), '0.8', 'weekly');
        $sitemap->add(route('pricing'), now(), '0.8', 'weekly');
        $sitemap->add(route('contact'), now(), '0.6', 'monthly');
        
        // Add blog posts
        BlogPost::published()->chunk(100, function ($posts) use ($sitemap) {
            foreach ($posts as $post) {
                $sitemap->add(
                    route('blog.show', $post->slug),
                    $post->updated_at,
                    '0.7',
                    'weekly'
                );
            }
        });
        
        return $sitemap->generate();
    }
    
    public function generateStructuredData($page, $data = [])
    {
        $baseData = [
            '@context' => 'https://schema.org',
            '@type' => 'SoftwareApplication',
            'name' => 'PharmaSaaS',
            'applicationCategory' => 'BusinessApplication',
            'operatingSystem' => 'Web',
            'offers' => [
                '@type' => 'Offer',
                'price' => '299000',
                'priceCurrency' => 'VND',
            ]
        ];
        
        return array_merge($baseData, $data);
    }
}
```

## Câu lệnh để bắt đầu
"Hãy tạo public marketing website với Laravel Blade + Tailwind CSS, bao gồm landing page, pricing, tenant registration và SEO optimization cho SaaS hiệu thuốc"
