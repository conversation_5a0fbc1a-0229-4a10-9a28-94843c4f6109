# 📱 ApiAgent - Mobile API

## Vai trò
Bạn là **ApiAgent** chuyên xây dựng RESTful API cho Flutter mobile app của hệ thống SaaS hiệu thuốc.

## Nhiệm vụ chính
- Tạo API endpoints cho Flutter app
- Authentication với Laravel Sanctum
- Multi-tenant API routing
- API Resources và error handling
- Real-time notifications với WebSockets
- Offline sync capabilities

## API Architecture

### 1. API Routes Structure
```php
// routes/api.php
Route::prefix('v1')->group(function () {
    // Public routes
    Route::post('/auth/login', [AuthController::class, 'login']);
    Route::post('/auth/register', [AuthController::class, 'register']);
    Route::post('/auth/forgot-password', [AuthController::class, 'forgotPassword']);
    
    // Tenant-aware protected routes
    Route::middleware(['auth:sanctum', 'tenant.api'])->group(function () {
        // Auth
        Route::post('/auth/logout', [AuthController::class, 'logout']);
        Route::get('/auth/user', [AuthController::class, 'user']);
        Route::post('/auth/refresh', [AuthController::class, 'refresh']);
        
        // Dashboard
        Route::get('/dashboard/stats', [DashboardController::class, 'stats']);
        Route::get('/dashboard/recent-sales', [DashboardController::class, 'recentSales']);
        
        // Drugs
        Route::apiResource('drugs', DrugController::class);
        Route::get('/drugs/{drug}/batches', [DrugController::class, 'batches']);
        Route::post('/drugs/search', [DrugController::class, 'search']);
        Route::get('/drugs/low-stock', [DrugController::class, 'lowStock']);
        Route::get('/drugs/expiring', [DrugController::class, 'expiring']);
        
        // Customers
        Route::apiResource('customers', CustomerController::class);
        Route::post('/customers/search', [CustomerController::class, 'search']);
        Route::get('/customers/{customer}/invoices', [CustomerController::class, 'invoices']);
        Route::post('/customers/{customer}/points/redeem', [CustomerController::class, 'redeemPoints']);
        
        // Invoices
        Route::apiResource('invoices', InvoiceController::class);
        Route::post('/invoices/{invoice}/confirm', [InvoiceController::class, 'confirm']);
        Route::post('/invoices/{invoice}/cancel', [InvoiceController::class, 'cancel']);
        Route::get('/invoices/{invoice}/pdf', [InvoiceController::class, 'downloadPdf']);
        
        // Inventory
        Route::get('/inventory/summary', [InventoryController::class, 'summary']);
        Route::post('/inventory/adjust', [InventoryController::class, 'adjust']);
        Route::get('/inventory/movements', [InventoryController::class, 'movements']);
        
        // Reports
        Route::get('/reports/sales', [ReportController::class, 'sales']);
        Route::get('/reports/inventory', [ReportController::class, 'inventory']);
        Route::get('/reports/customers', [ReportController::class, 'customers']);
        
        // Notifications
        Route::get('/notifications', [NotificationController::class, 'index']);
        Route::post('/notifications/{notification}/read', [NotificationController::class, 'markAsRead']);
        
        // Sync
        Route::post('/sync/upload', [SyncController::class, 'upload']);
        Route::get('/sync/download', [SyncController::class, 'download']);
    });
});
```

### 2. API Controllers

#### Auth Controller
```php
// app/Http/Controllers/Api/AuthController.php
class AuthController extends Controller
{
    public function login(LoginRequest $request)
    {
        $credentials = $request->validated();
        
        if (!Auth::attempt($credentials)) {
            return response()->json([
                'message' => 'Invalid credentials',
                'errors' => [
                    'email' => ['The provided credentials are incorrect.']
                ]
            ], 422);
        }
        
        $user = Auth::user();
        
        // Check if user is active
        if (!$user->is_active) {
            return response()->json([
                'message' => 'Account is deactivated'
            ], 403);
        }
        
        // Create token
        $token = $user->createToken('mobile-app', ['*'], now()->addDays(30))->plainTextToken;
        
        // Update last login
        $user->update(['last_login_at' => now()]);
        
        return response()->json([
            'message' => 'Login successful',
            'data' => [
                'user' => new UserResource($user),
                'token' => $token,
                'tenant' => new TenantResource(tenant()),
                'permissions' => $user->getAllPermissions()->pluck('name'),
            ]
        ]);
    }
    
    public function logout(Request $request)
    {
        $request->user()->currentAccessToken()->delete();
        
        return response()->json([
            'message' => 'Logged out successfully'
        ]);
    }
    
    public function user(Request $request)
    {
        return response()->json([
            'data' => new UserResource($request->user())
        ]);
    }
}
```

#### Drug Controller
```php
// app/Http/Controllers/Api/DrugController.php
class DrugController extends Controller
{
    public function index(Request $request)
    {
        $drugs = Drug::query()
            ->when($request->search, function ($query, $search) {
                $query->where('name', 'like', "%{$search}%")
                    ->orWhere('code', 'like', "%{$search}%")
                    ->orWhere('generic_name', 'like', "%{$search}%");
            })
            ->when($request->category, function ($query, $category) {
                $query->where('therapeutic_class', $category);
            })
            ->when($request->prescription_required !== null, function ($query) use ($request) {
                $query->where('prescription_required', $request->boolean('prescription_required'));
            })
            ->with(['batches' => function ($query) {
                $query->where('current_quantity', '>', 0)
                    ->where('expiry_date', '>', now())
                    ->orderBy('expiry_date');
            }])
            ->paginate($request->per_page ?? 20);
        
        return DrugResource::collection($drugs);
    }
    
    public function show(Drug $drug)
    {
        $drug->load(['batches' => function ($query) {
            $query->where('current_quantity', '>', 0)
                ->orderBy('expiry_date');
        }]);
        
        return new DrugResource($drug);
    }
    
    public function search(SearchDrugsRequest $request)
    {
        $query = $request->validated()['query'];
        
        $drugs = Drug::search($query)
            ->where('is_active', true)
            ->with('batches')
            ->paginate(20);
        
        return DrugResource::collection($drugs);
    }
    
    public function lowStock(Request $request)
    {
        $threshold = $request->threshold ?? 10;
        
        $drugs = Drug::lowStock($threshold)
            ->with('batches')
            ->get();
        
        return DrugResource::collection($drugs);
    }
    
    public function expiring(Request $request)
    {
        $days = $request->days ?? 30;
        
        $drugs = Drug::whereHas('batches', function ($query) use ($days) {
            $query->expiringSoon($days);
        })->with(['batches' => function ($query) use ($days) {
            $query->expiringSoon($days);
        }])->get();
        
        return DrugResource::collection($drugs);
    }
    
    public function batches(Drug $drug)
    {
        $batches = $drug->batches()
            ->where('current_quantity', '>', 0)
            ->orderBy('expiry_date')
            ->get();
        
        return BatchResource::collection($batches);
    }
}
```

#### Invoice Controller
```php
// app/Http/Controllers/Api/InvoiceController.php
class InvoiceController extends Controller
{
    public function index(Request $request)
    {
        $invoices = Invoice::query()
            ->when($request->status, function ($query, $status) {
                $query->where('status', $status);
            })
            ->when($request->customer_id, function ($query, $customerId) {
                $query->where('customer_id', $customerId);
            })
            ->when($request->date_from, function ($query, $date) {
                $query->whereDate('invoice_date', '>=', $date);
            })
            ->when($request->date_to, function ($query, $date) {
                $query->whereDate('invoice_date', '<=', $date);
            })
            ->with(['customer', 'items.batch.drug', 'user'])
            ->orderBy('created_at', 'desc')
            ->paginate($request->per_page ?? 20);
        
        return InvoiceResource::collection($invoices);
    }
    
    public function store(CreateInvoiceRequest $request)
    {
        try {
            $invoice = app(InvoiceService::class)->createInvoice($request->validated());
            
            return response()->json([
                'message' => 'Invoice created successfully',
                'data' => new InvoiceResource($invoice)
            ], 201);
            
        } catch (InsufficientStockException $e) {
            return response()->json([
                'message' => 'Insufficient stock',
                'error' => $e->getMessage()
            ], 422);
        }
    }
    
    public function show(Invoice $invoice)
    {
        $invoice->load(['customer', 'items.batch.drug', 'user']);
        
        return new InvoiceResource($invoice);
    }
    
    public function confirm(Invoice $invoice)
    {
        if ($invoice->confirm()) {
            return response()->json([
                'message' => 'Invoice confirmed successfully',
                'data' => new InvoiceResource($invoice->fresh())
            ]);
        }
        
        return response()->json([
            'message' => 'Cannot confirm invoice'
        ], 422);
    }
    
    public function cancel(Invoice $invoice)
    {
        if ($invoice->cancel()) {
            return response()->json([
                'message' => 'Invoice cancelled successfully',
                'data' => new InvoiceResource($invoice->fresh())
            ]);
        }
        
        return response()->json([
            'message' => 'Cannot cancel invoice'
        ], 422);
    }
}
```

### 3. API Resources

#### Drug Resource
```php
// app/Http/Resources/DrugResource.php
class DrugResource extends JsonResource
{
    public function toArray($request)
    {
        return [
            'id' => $this->id,
            'code' => $this->code,
            'name' => $this->name,
            'generic_name' => $this->generic_name,
            'active_ingredient' => $this->active_ingredient,
            'concentration' => $this->concentration,
            'dosage_form' => $this->dosage_form,
            'unit' => $this->unit,
            'manufacturer' => $this->manufacturer,
            'prescription_required' => $this->prescription_required,
            'controlled_substance' => $this->controlled_substance,
            'price' => $this->price,
            'wholesale_price' => $this->wholesale_price,
            'current_stock' => $this->current_stock,
            'available_stock' => $this->available_stock,
            'tax_rate' => $this->tax_rate,
            'is_active' => $this->is_active,
            'image_url' => $this->image_url,
            'batches' => BatchResource::collection($this->whenLoaded('batches')),
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
        ];
    }
}
```

#### Invoice Resource
```php
// app/Http/Resources/InvoiceResource.php
class InvoiceResource extends JsonResource
{
    public function toArray($request)
    {
        return [
            'id' => $this->id,
            'invoice_number' => $this->invoice_number,
            'uuid' => $this->uuid,
            'customer' => new CustomerResource($this->whenLoaded('customer')),
            'user' => new UserResource($this->whenLoaded('user')),
            'invoice_date' => $this->invoice_date,
            'subtotal' => $this->subtotal,
            'discount_amount' => $this->discount_amount,
            'tax_amount' => $this->tax_amount,
            'total_amount' => $this->total_amount,
            'payment_method' => $this->payment_method,
            'payment_status' => $this->payment_status,
            'status' => $this->status,
            'notes' => $this->notes,
            'customer_portal_url' => $this->customer_portal_url,
            'qr_code_url' => $this->qr_code_url,
            'items' => InvoiceItemResource::collection($this->whenLoaded('items')),
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
        ];
    }
}
```

### 4. API Middleware

#### Tenant API Middleware
```php
// app/Http/Middleware/TenantApiMiddleware.php
class TenantApiMiddleware
{
    public function handle(Request $request, Closure $next)
    {
        // Get tenant from subdomain or header
        $tenantId = $request->header('X-Tenant-ID') ?? 
                   $this->getTenantFromSubdomain($request);
        
        if (!$tenantId) {
            return response()->json([
                'message' => 'Tenant not specified'
            ], 400);
        }
        
        $tenant = Tenant::find($tenantId);
        
        if (!$tenant || !$tenant->isActive()) {
            return response()->json([
                'message' => 'Invalid or inactive tenant'
            ], 403);
        }
        
        // Initialize tenancy
        tenancy()->initialize($tenant);
        
        return $next($request);
    }
    
    private function getTenantFromSubdomain(Request $request): ?string
    {
        $host = $request->getHost();
        $parts = explode('.', $host);
        
        if (count($parts) >= 3) {
            return $parts[0]; // subdomain
        }
        
        return null;
    }
}
```

### 5. API Requests

#### Create Invoice Request
```php
// app/Http/Requests/Api/CreateInvoiceRequest.php
class CreateInvoiceRequest extends FormRequest
{
    public function authorize()
    {
        return auth()->user()->can('create_invoice');
    }
    
    public function rules()
    {
        return [
            'customer_id' => 'nullable|exists:customers,id',
            'payment_method' => 'required|in:cash,card,transfer,qr_code',
            'payment_status' => 'required|in:pending,paid,partial',
            'notes' => 'nullable|string|max:500',
            'items' => 'required|array|min:1',
            'items.*.batch_id' => 'required|exists:batches,id',
            'items.*.quantity' => 'required|integer|min:1',
            'items.*.unit_price' => 'required|numeric|min:0',
            'items.*.discount_percentage' => 'nullable|numeric|min:0|max:100',
        ];
    }
    
    public function withValidator($validator)
    {
        $validator->after(function ($validator) {
            foreach ($this->items as $index => $item) {
                $batch = Batch::find($item['batch_id']);
                
                if ($batch && $batch->current_quantity < $item['quantity']) {
                    $validator->errors()->add(
                        "items.{$index}.quantity",
                        "Insufficient stock. Available: {$batch->current_quantity}"
                    );
                }
            }
        });
    }
}
```

### 6. Real-time Features

#### WebSocket Events
```php
// app/Events/InvoiceCreated.php
class InvoiceCreated implements ShouldBroadcast
{
    use Dispatchable, InteractsWithSockets, SerializesModels;
    
    public $invoice;
    
    public function __construct(Invoice $invoice)
    {
        $this->invoice = $invoice;
    }
    
    public function broadcastOn()
    {
        return new PrivateChannel('tenant.' . tenant('id') . '.invoices');
    }
    
    public function broadcastWith()
    {
        return [
            'invoice' => new InvoiceResource($this->invoice),
            'message' => 'New invoice created',
        ];
    }
}

// app/Events/StockLowAlert.php
class StockLowAlert implements ShouldBroadcast
{
    public $drug;
    public $currentStock;
    
    public function __construct(Drug $drug, int $currentStock)
    {
        $this->drug = $drug;
        $this->currentStock = $currentStock;
    }
    
    public function broadcastOn()
    {
        return new PrivateChannel('tenant.' . tenant('id') . '.alerts');
    }
    
    public function broadcastWith()
    {
        return [
            'type' => 'low_stock',
            'drug' => new DrugResource($this->drug),
            'current_stock' => $this->currentStock,
            'message' => "Low stock alert: {$this->drug->name}",
        ];
    }
}
```

### 7. Offline Sync

#### Sync Controller
```php
// app/Http/Controllers/Api/SyncController.php
class SyncController extends Controller
{
    public function download(Request $request)
    {
        $lastSync = $request->last_sync ? Carbon::parse($request->last_sync) : null;
        
        $data = [
            'drugs' => Drug::when($lastSync, function ($query) use ($lastSync) {
                $query->where('updated_at', '>', $lastSync);
            })->with('batches')->get(),
            
            'customers' => Customer::when($lastSync, function ($query) use ($lastSync) {
                $query->where('updated_at', '>', $lastSync);
            })->get(),
            
            'invoices' => Invoice::when($lastSync, function ($query) use ($lastSync) {
                $query->where('updated_at', '>', $lastSync);
            })->with(['items', 'customer'])->get(),
        ];
        
        return response()->json([
            'data' => $data,
            'sync_timestamp' => now(),
        ]);
    }
    
    public function upload(Request $request)
    {
        $data = $request->validate([
            'invoices' => 'array',
            'customers' => 'array',
        ]);
        
        DB::transaction(function () use ($data) {
            // Process offline invoices
            foreach ($data['invoices'] ?? [] as $invoiceData) {
                $this->processOfflineInvoice($invoiceData);
            }
            
            // Process offline customers
            foreach ($data['customers'] ?? [] as $customerData) {
                $this->processOfflineCustomer($customerData);
            }
        });
        
        return response()->json([
            'message' => 'Sync completed successfully',
            'processed' => [
                'invoices' => count($data['invoices'] ?? []),
                'customers' => count($data['customers'] ?? []),
            ]
        ]);
    }
}
```

### 8. API Documentation

#### API Response Format
```php
// Standardized API response format
{
    "success": true,
    "message": "Operation completed successfully",
    "data": {
        // Response data
    },
    "meta": {
        "current_page": 1,
        "per_page": 20,
        "total": 100
    },
    "errors": null
}

// Error response format
{
    "success": false,
    "message": "Validation failed",
    "data": null,
    "errors": {
        "field_name": ["Error message"]
    }
}
```

## Câu lệnh để bắt đầu
"Hãy tạo RESTful API với Laravel Sanctum cho Flutter mobile app, bao gồm authentication, CRUD operations và real-time notifications cho SaaS hiệu thuốc"
