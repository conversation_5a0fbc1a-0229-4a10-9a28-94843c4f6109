# 🚀 Hướng dẫn sử dụng AI Agents

## Tổng quan
Hướng dẫn chi tiết cách sử dụng từng AI Agent trong Augment Code để phát triển hệ thống hiệu thuốc.

## 📋 Quy trình phát triển đề xuất

### Phase 1: Setup & Core (Tuần 1-2)
1. **DatabaseAgent** → Tạo migrations và seeders
2. **AuthAgent** → <PERSON>ệ thống đăng nhập và phân quyền
3. **BackendAgent** → Models và Controllers cơ bản

### Phase 2: Business Logic (Tuần 3-4)
4. **DrugAgent** → Quản lý thuốc và lô hàng
5. **CustomerAgent** → Quản lý khách hàng
6. **InvoiceAgent** → <PERSON><PERSON> thống hóa đơn

### Phase 3: Testing & Documentation (Tuần 5-6)
7. **TestAgent** → Unit tests và Feature tests
8. **DocAgent** → API documentation và user guide

## 🎯 Cách sử dụng từng Agent

### 1. BackendAgent - Bắt đầu với Core Models

**Bước 1**: Mở Augment Code trong VSCode
**Bước 2**: Copy prompt từ `agents-prompts/backend-agent.md`
**Bước 3**: Sử dụng prompt:

```
Hãy tạo Drug model với đầy đủ relationships và DrugController với CRUD operations theo Repository pattern. 

Yêu cầu:
- Model Drug với các fields: code, name, active_ingredient, dosage_form, unit, manufacturer, prescription_required, price
- DrugRepository interface và implementation
- DrugController với index, show, store, update, destroy
- DrugResource cho API response
- Form Request cho validation
- Soft deletes
```

**Kết quả mong đợi**: 
- `app/Models/Drug.php`
- `app/Repositories/DrugRepository.php`
- `app/Http/Controllers/DrugController.php`
- `app/Http/Resources/DrugResource.php`
- `app/Http/Requests/StoreDrugRequest.php`

### 2. DrugAgent - Quản lý dược phẩm

**Prompt mẫu**:
```
Hãy tạo BatchService để quản lý lô hàng theo FEFO (First Expired, First Out).

Yêu cầu:
- Batch model với drug_id, batch_number, expiry_date, current_quantity
- BatchService với methods: getOldestBatch(), allocateStock(), checkExpiry()
- InventoryService để kiểm tra tồn kho
- Cảnh báo thuốc sắp hết hạn (< 30 ngày)
- API endpoints cho quản lý lô hàng
```

### 3. InvoiceAgent - Hệ thống hóa đơn

**Prompt mẫu**:
```
Hãy tạo InvoiceService với chức năng tính thuế GTGT và sinh mã hóa đơn tự động.

Yêu cầu:
- Sinh mã hóa đơn format: HD{YYYYMMDD}{NNNN}
- Tính thuế GTGT: thuốc kê đơn 5%, không kê đơn 10%
- TaxCalculator class
- Mock API gửi hóa đơn lên Tổng cục Thuế
- Digital signature cho hóa đơn điện tử
- PDF export cho hóa đơn
```

### 4. CustomerAgent - Quản lý khách hàng

**Prompt mẫu**:
```
Hãy tạo Customer model với hệ thống tích điểm và CustomerService.

Yêu cầu:
- Customer model với loyalty_points, customer_group, total_spent
- LoyaltyService: earnPoints(), redeemPoints(), expirePoints()
- Phân loại khách hàng: retail, wholesale, vip
- Tự động nâng hạng dựa trên tổng mua hàng
- CustomerAnalyticsService cho phân tích RFM
```

### 5. AuthAgent - Phân quyền

**Prompt mẫu**:
```
Hãy tạo hệ thống authentication với JWT, phân quyền RBAC và audit log.

Yêu cầu:
- Laravel Sanctum cho API authentication
- Roles: super_admin, manager, pharmacist, cashier
- Permissions theo modules: drugs.*, sales.*, inventory.*
- AuditLog cho các thao tác quan trọng
- Two-factor authentication với TOTP
- Password policy và rate limiting
```

### 6. TestAgent - Testing

**Prompt mẫu**:
```
Hãy tạo test suite hoàn chỉnh cho DrugService với unit tests và feature tests.

Yêu cầu:
- DrugServiceTest: searchDrugs(), checkAvailability()
- DrugApiTest: CRUD operations với authentication
- Factory classes cho Drug, Batch, Customer
- SalesProcessTest: complete sales flow
- Performance test cho drug search với 10k records
- Test coverage > 80%
```

### 7. DocAgent - Documentation

**Prompt mẫu**:
```
Hãy tạo Swagger documentation hoàn chỉnh cho Drug API và Invoice API.

Yêu cầu:
- OpenAPI 3.0 specification
- Schemas cho Drug, Invoice, Customer
- Request/Response examples
- Authentication với Bearer token
- Error response formats
- Postman collection export
```

## 🔄 Workflow với Augment Code

### Quy trình làm việc hàng ngày:

1. **Morning Setup**:
   ```
   - Mở VSCode với Augment extension
   - Pull latest code từ git
   - Review tasks trong project board
   ```

2. **Development Loop**:
   ```
   - Chọn Agent phù hợp với task
   - Copy prompt từ agents-prompts/
   - Customize prompt theo yêu cầu cụ thể
   - Run Augment Agent
   - Review và refine code
   - Commit changes
   ```

3. **Testing & Review**:
   ```
   - Sử dụng TestAgent để tạo tests
   - Run test suite
   - Code review với team
   - Deploy to staging
   ```

## 💡 Tips & Best Practices

### 1. Prompt Engineering
- **Specific**: Càng cụ thể càng tốt về requirements
- **Context**: Cung cấp context về business domain
- **Examples**: Đưa ra examples về format mong muốn
- **Constraints**: Nêu rõ limitations và constraints

### 2. Code Quality
- **Review**: Luôn review code được generate
- **Refactor**: Sử dụng Augment để refactor code
- **Standards**: Tuân thủ coding standards của team
- **Testing**: Viết tests cho mọi feature mới

### 3. Collaboration
- **Documentation**: Update docs sau mỗi feature
- **Communication**: Share prompts hiệu quả với team
- **Version Control**: Commit frequently với clear messages
- **Code Review**: Peer review cho generated code

## 🎨 Customization

### Tùy chỉnh Prompts
Bạn có thể customize các prompts theo nhu cầu:

```markdown
# Custom BackendAgent cho dự án cụ thể

## Context bổ sung
- Sử dụng UUID thay vì auto-increment ID
- Implement soft deletes cho tất cả models
- API response format theo JSON:API specification
- Validation messages bằng tiếng Việt

## Coding standards riêng
- Repository pattern bắt buộc
- Service layer cho business logic
- Event-driven architecture
- Queue jobs cho heavy operations
```

### Environment-specific Prompts
```markdown
# Development Environment
- Enable query logging
- Detailed error messages
- Debug toolbar
- Faker data generation

# Production Environment  
- Error logging to external service
- Performance monitoring
- Security headers
- Rate limiting
```

## 📊 Tracking Progress

### Checklist cho từng Phase:

**Phase 1 - Core Setup**:
- [ ] Database migrations created
- [ ] User authentication working
- [ ] Basic CRUD for Drug, Customer
- [ ] API endpoints responding
- [ ] Basic tests passing

**Phase 2 - Business Logic**:
- [ ] Inventory management working
- [ ] Invoice generation with tax
- [ ] Customer loyalty system
- [ ] Stock allocation (FEFO)
- [ ] Integration tests passing

**Phase 3 - Polish**:
- [ ] Full test coverage
- [ ] API documentation complete
- [ ] User manual written
- [ ] Performance optimized
- [ ] Security audit passed

## 🆘 Troubleshooting

### Common Issues:

1. **Generated code không compile**:
   - Check dependencies trong composer.json
   - Verify namespace imports
   - Run `composer dump-autoload`

2. **Tests failing**:
   - Check database seeding
   - Verify factory definitions
   - Clear cache: `php artisan cache:clear`

3. **API responses không đúng format**:
   - Review API Resource classes
   - Check controller return statements
   - Verify middleware stack

### Getting Help:
- Use Augment's "Explain" feature cho code phức tạp
- Ask specific questions về business logic
- Request code improvements và optimizations
