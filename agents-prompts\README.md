# 🤖 AI Agents Collection - SaaS Pharmacy Management System

Bộ sưu tập AI Agent prompts đ<PERSON><PERSON><PERSON> thiết kế đặc biệt cho việc phát triển **hệ thống SaaS multi-tenant quản lý hiệu thuốc** với **Laravel 11 + Filament 3**.

## 🏗️ System Architecture

**Multi-tenant SaaS Platform** với kiến trúc:
- **Platform Provider** (Super Admin) → **Pharmacy Owners** → **Staff** + **Customer Portal**
- **Laravel 11** + **Filament 3** + **Blade** + **Livewire**
- **Flutter Mobile App** + **Public Frontend** + **Customer Portal**
- **Database per tenant** strategy

## 📁 AI Agents Overview

| Agent | File | Mô tả | Tech Stack | Ưu tiên |
|-------|------|-------|------------|---------|
| 🏢 **TenantAgent** | `tenant-agent.md` | Multi-tenancy setup | Laravel Tenancy + Database per tenant | ⭐⭐⭐ |
| 🎨 **FilamentAgent** | `filament-agent.md` | Admin panel resources | Filament 3 Resources + Pages + Widgets | ⭐⭐⭐ |
| 🔐 **AuthAgent** | `auth-agent.md` | Authentication & roles | Filament Shield + Spatie Permission | ⭐⭐⭐ |
| 💊 **DrugAgent** | `drug-agent.md` | Inventory management | Filament forms + FEFO logic | ⭐⭐⭐ |
| 🧾 **InvoiceAgent** | `invoice-agent.md` | Sales & invoicing | Filament + PDF + Tax calculation | ⭐⭐⭐ |
| 👥 **CustomerAgent** | `customer-agent.md` | Customer management | Filament + Customer portal | ⭐⭐ |
| 📱 **ApiAgent** | `api-agent.md` | Mobile API | Laravel API + Sanctum | ⭐⭐ |
| 🌐 **PublicAgent** | `public-agent.md` | Marketing website | Laravel Blade + Tailwind | ⭐⭐ |
| 🧪 **TestAgent** | `test-agent.md` | Testing suite | Livewire tests + API tests | ⭐⭐ |
| 📖 **DocAgent** | `doc-agent.md` | Documentation | Multi-interface docs | ⭐ |

## 🗄️ Supporting Files

| File | Mô tả |
|------|-------|
| `database-schema.md` | Complete database schema với indexes |

## 🚀 Quick Start Guide

### 1. Khởi tạo Laravel 11 Project
```bash
composer create-project laravel/laravel:^11.0 pharmacy-saas
cd pharmacy-saas
```

### 2. Cài đặt Core Dependencies
```bash
# Filament 3
composer require filament/filament:"^3.0"
php artisan filament:install --panels

# Multi-tenancy
composer require stancl/tenancy

# Additional packages
composer require spatie/laravel-permission
composer require laravel/sanctum
composer require barryvdh/laravel-dompdf
```

### 3. Quy trình phát triển với AI Agents

#### Phase 1: Foundation Setup (Tuần 1-2)
1. **TenantAgent** → Multi-tenancy với database per tenant
2. **AuthAgent** → Filament authentication + RBAC
3. **FilamentAgent** → Basic admin panel resources

#### Phase 2: Core Business Logic (Tuần 3-4)
4. **DrugAgent** → Inventory management với FEFO
5. **CustomerAgent** → Customer management + loyalty
6. **InvoiceAgent** → Sales system + tax calculation

#### Phase 3: Extensions (Tuần 5-6)
7. **ApiAgent** → Mobile API cho Flutter app
8. **PublicAgent** → Marketing website + tenant registration

#### Phase 4: Quality Assurance (Tuần 7-8)
9. **TestAgent** → Comprehensive testing
10. **DocAgent** → Documentation + user guides

## 💡 Cách sử dụng với Augment Code

### Trong VSCode:
1. Mở **Augment Code extension**
2. Chọn **"Ask Agent"**
3. Copy prompt từ file tương ứng (VD: `tenant-agent.md`)
4. Customize theo yêu cầu cụ thể
5. Execute và review code

### Ví dụ workflow:
```
1. Mở agents-prompts/tenant-agent.md
2. Copy toàn bộ nội dung
3. Paste vào Augment Agent
4. Thêm yêu cầu cụ thể:
   "Hãy thiết lập multi-tenancy với subdomain routing 
   và tạo TenantService để manage pharmacy tenants"
5. Execute và review generated code
```

## 🎯 Business Features

### Core Functionality:
- ✅ **Multi-tenant SaaS** với database per tenant
- ✅ **Filament 3 admin panel** cho từng hiệu thuốc
- ✅ **Inventory management** với FEFO (First Expired, First Out)
- ✅ **Sales & invoicing** với thuế GTGT tự động
- ✅ **Customer management** với loyalty program
- ✅ **Mobile API** cho Flutter app
- ✅ **Customer portal** để xem hóa đơn với QR code
- ✅ **Public website** để marketing và registration

### Compliance & Regulations:
- 🏥 **Thuốc kê đơn** và không kê đơn
- 💰 **Thuế GTGT** (5% thuốc kê đơn, 10% không kê đơn)
- 📋 **Hóa đơn điện tử** theo quy định Việt Nam
- 🔒 **Multi-tenant security** và data isolation

## 🛠️ Tech Stack Details

### Backend:
- **Framework**: Laravel 11
- **Admin Panel**: Filament 3
- **Multi-tenancy**: Stancl/Tenancy
- **Authentication**: Filament Shield + Spatie Permission
- **Database**: MySQL 8.0 (database per tenant)
- **Cache**: Redis
- **Queue**: Laravel Queue + Redis

### Frontend:
- **Admin**: Filament 3 + Livewire
- **Public**: Laravel Blade + Tailwind CSS
- **Mobile**: Flutter (separate app)

### Infrastructure:
- **Server**: Ubuntu 20.04 + Nginx
- **SSL**: Let's Encrypt
- **Monitoring**: Laravel Telescope
- **Backup**: Database backup per tenant

## 📊 Project Structure

```
pharmacy-saas/
├── agents-prompts/          # AI Agent prompts
├── app/
│   ├── Filament/           # Filament resources & pages
│   │   ├── Resources/      # CRUD resources
│   │   ├── Pages/          # Custom pages
│   │   └── Widgets/        # Dashboard widgets
│   ├── Models/             # Multi-tenant models
│   ├── Services/           # Business logic services
│   ├── Http/
│   │   ├── Controllers/    # Web controllers
│   │   └── Api/           # API controllers
│   └── Policies/          # Authorization policies
├── database/
│   ├── migrations/
│   │   ├── central/       # Central database migrations
│   │   └── tenant/        # Tenant database migrations
│   └── seeders/           # Database seeders
├── resources/
│   ├── views/
│   │   ├── filament/      # Filament customizations
│   │   ├── public/        # Public website views
│   │   └── customer-portal/ # Customer portal views
│   └── css/               # Tailwind CSS
├── routes/
│   ├── web.php            # Central domain routes
│   ├── tenant.php         # Tenant domain routes
│   └── api.php            # API routes
└── tests/
    ├── Unit/              # Unit tests
    ├── Feature/           # Feature tests
    └── Browser/           # Browser tests
```

## 🔧 Environment Configuration

### .env Template:
```env
APP_NAME="PharmaSaaS"
APP_ENV=production
APP_DEBUG=false
APP_URL=https://pharmasaas.com

# Central Database
DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=pharmasaas_central
DB_USERNAME=pharmasaas
DB_PASSWORD=secure_password

# Tenancy Configuration
TENANCY_DATABASE_AUTO_DELETE=false
TENANCY_DATABASE_AUTO_DELETE_USER=false

# Cache & Queue
REDIS_HOST=127.0.0.1
REDIS_PASSWORD=null
REDIS_PORT=6379
CACHE_DRIVER=redis
QUEUE_CONNECTION=redis

# Mail Configuration
MAIL_MAILER=smtp
MAIL_HOST=smtp.gmail.com
MAIL_PORT=587
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=app_password
MAIL_ENCRYPTION=tls

# Filament Configuration
FILAMENT_DOMAIN=null
FILAMENT_PATH=admin
```

## 📈 Development Roadmap

### Sprint 1 (Week 1-2): Foundation
- [ ] Multi-tenancy setup với database per tenant
- [ ] Filament 3 admin panel cơ bản
- [ ] Authentication và role management
- [ ] Basic CRUD cho Drug, Customer

### Sprint 2 (Week 3-4): Core Features
- [ ] Inventory management với FEFO logic
- [ ] Sales system với tax calculation
- [ ] Customer loyalty program
- [ ] Invoice generation với PDF

### Sprint 3 (Week 5-6): Extensions
- [ ] Mobile API cho Flutter app
- [ ] Customer portal với QR code payment
- [ ] Public website với tenant registration
- [ ] Real-time notifications

### Sprint 4 (Week 7-8): Polish
- [ ] Comprehensive testing suite
- [ ] Performance optimization
- [ ] Security audit
- [ ] Documentation completion

## 🤝 Contributing Guidelines

### Code Standards:
- **PSR-12** coding standard
- **Repository pattern** cho data access
- **Service layer** cho business logic
- **Comprehensive testing** (>80% coverage)
- **Multi-tenant compatibility** cho tất cả features

### Git Workflow:
```bash
git checkout -b feature/inventory-management
# Develop with AI Agents
git add .
git commit -m "feat: implement FEFO inventory management"
git push origin feature/inventory-management
# Create Pull Request
```

## 📞 Support & Resources

### Documentation:
- 📚 [Laravel 11 Documentation](https://laravel.com/docs/11.x)
- 🎨 [Filament 3 Documentation](https://filamentphp.com/docs/3.x)
- 🏢 [Laravel Tenancy Documentation](https://tenancyforlaravel.com/docs)
- 🤖 [Augment Code Guide](https://augmentcode.com/docs)

### Community:
- 💬 **Discord**: #pharmasaas-dev
- 📧 **Email**: <EMAIL>
- 🐛 **Issues**: GitHub Issues
- 📖 **Wiki**: Project Wiki

---

**🚀 Ready to build the future of pharmacy management with AI-powered development!**

*Powered by Augment Code AI Agents for rapid SaaS development*
