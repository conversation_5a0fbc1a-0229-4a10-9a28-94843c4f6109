# 🤖 AI Agents Collection - <PERSON><PERSON> thống Hiệu thuốc

B<PERSON> sưu tập các AI Agent prompts đ<PERSON><PERSON><PERSON> thiết kế đặc biệt cho việc phát triển hệ thống quản lý bán hàng hiệu thuốc bằng Laravel.

## 📁 Danh sách Agents

| Agent | File | Mô tả | Ưu tiên |
|-------|------|-------|---------|
| 🧱 **BackendAgent** | `backend-agent.md` | Xây dựng Models, Controllers, Repository pattern | ⭐⭐⭐ |
| 🧾 **InvoiceAgent** | `invoice-agent.md` | Hệ thống hóa đơn, tính thuế GTGT, ký số | ⭐⭐⭐ |
| 💊 **DrugAgent** | `drug-agent.md` | Quản lý thuốc, lô hàng, FEFO, tương tác thuốc | ⭐⭐⭐ |
| 🧍‍♂️ **CustomerAgent** | `customer-agent.md` | Qu<PERSON>n lý khách hà<PERSON>, tích đi<PERSON>, phân loại | ⭐⭐ |
| 🔐 **AuthAgent** | `auth-agent.md` | Authentication, RBAC, audit log, 2FA | ⭐⭐⭐ |
| 🧪 **TestAgent** | `test-agent.md` | Unit tests, Feature tests, Performance tests | ⭐⭐ |
| 📖 **DocAgent** | `doc-agent.md` | API docs, Swagger, User manual | ⭐ |

## 🗄️ Database & Schema

| File | Mô tả |
|------|-------|
| `database-schema.md` | Complete database schema với indexes |
| `usage-guide.md` | Hướng dẫn sử dụng chi tiết từng Agent |

## 🚀 Quick Start

### 1. Khởi tạo dự án Laravel
```bash
composer create-project laravel/laravel pharmacy-system
cd pharmacy-system
```

### 2. Cài đặt dependencies cơ bản
```bash
composer require laravel/sanctum spatie/laravel-permission
composer require --dev laravel/telescope
php artisan vendor:publish --provider="Laravel\Sanctum\SanctumServiceProvider"
```

### 3. Sử dụng Agents theo thứ tự

#### Phase 1: Core Setup (Tuần 1)
1. **AuthAgent** → Tạo hệ thống đăng nhập và phân quyền
2. **BackendAgent** → Tạo Models cơ bản (Drug, Customer, Invoice)

#### Phase 2: Business Logic (Tuần 2-3)  
3. **DrugAgent** → Quản lý thuốc và inventory
4. **CustomerAgent** → Hệ thống khách hàng và loyalty
5. **InvoiceAgent** → Hóa đơn và thuế

#### Phase 3: Quality Assurance (Tuần 4)
6. **TestAgent** → Viết tests cho tất cả modules
7. **DocAgent** → Tạo documentation

## 💡 Cách sử dụng với Augment Code

### Trong VSCode:
1. Mở Augment Code extension
2. Chọn "Ask Agent" 
3. Copy prompt từ file tương ứng
4. Customize theo yêu cầu cụ thể
5. Execute và review code

### Ví dụ prompt:
```
[Copy nội dung từ backend-agent.md]

Yêu cầu bổ sung:
- Sử dụng UUID cho primary keys
- Thêm soft deletes
- API response theo JSON:API format
```

## 🎯 Business Requirements

### Chức năng chính:
- ✅ Quản lý thuốc và lô hàng (FEFO)
- ✅ Bán hàng và xuất hóa đơn điện tử
- ✅ Quản lý khách hàng và tích điểm
- ✅ Kiểm soát tồn kho và cảnh báo hết hạn
- ✅ Phân quyền nhân viên
- ✅ Báo cáo doanh thu và tồn kho

### Tuân thủ quy định:
- 🏥 Thuốc kê đơn và không kê đơn
- 💰 Thuế GTGT (5% và 10%)
- 📋 Hóa đơn điện tử theo quy định
- 🔒 Bảo mật thông tin khách hàng

## 🛠️ Tech Stack

### Backend:
- **Framework**: Laravel 10+
- **Database**: MySQL 8.0
- **Authentication**: Laravel Sanctum
- **Authorization**: Spatie Permission
- **Testing**: PHPUnit + Laravel Dusk

### Frontend (Optional):
- **Framework**: Vue.js 3 / React
- **Build Tool**: Vite
- **UI Library**: Tailwind CSS

### DevOps:
- **Server**: Ubuntu 20.04 + Nginx
- **Cache**: Redis
- **Queue**: Laravel Queue
- **Monitoring**: Laravel Telescope

## 📊 Project Structure

```
pharmacy-system/
├── agents-prompts/          # AI Agent prompts
├── app/
│   ├── Models/             # Eloquent models
│   ├── Http/
│   │   ├── Controllers/    # API controllers
│   │   ├── Resources/      # API resources
│   │   └── Requests/       # Form requests
│   ├── Services/           # Business logic
│   ├── Repositories/       # Data access layer
│   └── Policies/          # Authorization policies
├── database/
│   ├── migrations/        # Database migrations
│   ├── seeders/          # Database seeders
│   └── factories/        # Model factories
├── tests/
│   ├── Unit/             # Unit tests
│   ├── Feature/          # Feature tests
│   └── Integration/      # Integration tests
└── docs/                 # API documentation
```

## 🔧 Environment Setup

### .env Configuration:
```env
APP_NAME="Pharmacy Management System"
APP_ENV=local
APP_DEBUG=true
APP_URL=http://localhost:8000

DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=pharmacy_db
DB_USERNAME=root
DB_PASSWORD=

SANCTUM_STATEFUL_DOMAINS=localhost:3000
SESSION_DRIVER=redis
CACHE_DRIVER=redis
QUEUE_CONNECTION=redis

MAIL_MAILER=smtp
MAIL_HOST=mailhog
MAIL_PORT=1025
```

## 📈 Development Roadmap

### Sprint 1 (Week 1): Foundation
- [ ] Project setup và environment
- [ ] Database schema và migrations
- [ ] Authentication system
- [ ] Basic CRUD operations

### Sprint 2 (Week 2): Core Features
- [ ] Drug management với batch tracking
- [ ] Customer management với loyalty
- [ ] Basic sales functionality

### Sprint 3 (Week 3): Advanced Features  
- [ ] Invoice generation với tax calculation
- [ ] Inventory management (FEFO)
- [ ] Digital signature integration

### Sprint 4 (Week 4): Testing & Documentation
- [ ] Comprehensive test suite
- [ ] API documentation
- [ ] User manual
- [ ] Performance optimization

## 🤝 Contributing

### Code Standards:
- PSR-12 coding standard
- Repository pattern cho data access
- Service layer cho business logic
- Comprehensive testing (>80% coverage)

### Git Workflow:
```bash
git checkout -b feature/drug-management
# Develop with AI Agents
git add .
git commit -m "feat: implement drug management with FEFO"
git push origin feature/drug-management
# Create Pull Request
```

## 📞 Support

### Resources:
- 📚 [Laravel Documentation](https://laravel.com/docs)
- 🤖 [Augment Code Guide](https://augmentcode.com/docs)
- 💬 Team Slack: #pharmacy-dev
- 📧 Email: <EMAIL>

### Troubleshooting:
1. Check `usage-guide.md` for common issues
2. Review generated code carefully
3. Run tests after each major change
4. Use Augment's "Explain" feature for complex code

---

**Happy Coding with AI Agents! 🚀**
