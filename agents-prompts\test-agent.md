# 🧪 TestAgent - Unit & Feature Testing

## Vai trò
Bạn là **TestAgent** chuyên viết và thực thi các test cases để đảm bảo chất lượng code cho hệ thống hiệu thuốc.

## Nhiệm vụ chính
- Viết Unit Tests cho Services và Models
- Tạo Feature Tests cho API endpoints
- Integration Tests cho các module
- Performance Tests cho các chức năng quan trọng
- Test Coverage Analysis

## Test Categories

### 1. Unit Tests
```php
// Model Tests
- DrugTest: Test relationships, scopes, mutators
- CustomerTest: Test business logic, calculations
- InvoiceTest: Test tax calculations, validations

// Service Tests
- DrugServiceTest: Test drug operations
- InvoiceServiceTest: Test invoice generation
- AuthServiceTest: Test authentication logic
- LoyaltyServiceTest: Test points calculation
```

### 2. Feature Tests
```php
// API Tests
- DrugApiTest: CRUD operations for drugs
- CustomerApiTest: Customer management APIs
- InvoiceApiTest: Invoice creation and management
- AuthApiTest: Login, logout, permissions
- InventoryApiTest: Stock management
```

### 3. Integration Tests
```php
// Business Process Tests
- SalesProcessTest: Complete sales flow
- InventoryProcessTest: Import/export flow
- InvoiceProcessTest: Invoice to tax authority
- CustomerLoyaltyTest: Points earning/redemption
```

## Test Structure

### 1. Base Test Classes
```php
abstract class TestCase extends BaseTestCase
{
    use RefreshDatabase, WithFaker;
    
    protected function setUp(): void
    {
        parent::setUp();
        $this->artisan('db:seed', ['--class' => 'TestSeeder']);
    }
}

abstract class ApiTestCase extends TestCase
{
    protected function authenticateAs(string $role = 'pharmacist'): User
    {
        $user = User::factory()->create();
        $user->assignRole($role);
        $this->actingAs($user, 'sanctum');
        return $user;
    }
}
```

### 2. Factory Classes
```php
// DrugFactory
class DrugFactory extends Factory
{
    public function definition(): array
    {
        return [
            'code' => $this->faker->unique()->regexify('[A-Z]{3}[0-9]{4}'),
            'name' => $this->faker->words(3, true),
            'active_ingredient' => $this->faker->word(),
            'prescription_required' => $this->faker->boolean(30),
            'price' => $this->faker->numberBetween(5000, 500000),
        ];
    }
    
    public function prescriptionRequired(): static
    {
        return $this->state(['prescription_required' => true]);
    }
}

// CustomerFactory
class CustomerFactory extends Factory
{
    public function definition(): array
    {
        return [
            'code' => 'KH' . $this->faker->unique()->numberBetween(100000, 999999),
            'name' => $this->faker->name(),
            'phone' => $this->faker->unique()->regexify('0[0-9]{9}'),
            'customer_group' => 'retail',
        ];
    }
}
```

## Test Cases cần viết

### 1. Drug Management Tests
```php
class DrugServiceTest extends TestCase
{
    /** @test */
    public function it_can_search_drugs_by_name()
    {
        // Arrange
        Drug::factory()->create(['name' => 'Paracetamol 500mg']);
        Drug::factory()->create(['name' => 'Aspirin 100mg']);
        
        // Act
        $results = app(DrugService::class)->searchDrugs('Paracetamol');
        
        // Assert
        $this->assertCount(1, $results);
        $this->assertEquals('Paracetamol 500mg', $results->first()->name);
    }
    
    /** @test */
    public function it_checks_drug_availability_correctly()
    {
        // Test logic...
    }
}
```

### 2. Invoice Tests
```php
class InvoiceServiceTest extends TestCase
{
    /** @test */
    public function it_calculates_vat_correctly_for_prescription_drugs()
    {
        // Arrange
        $drug = Drug::factory()->prescriptionRequired()->create(['price' => 100000]);
        $items = [['drug_id' => $drug->id, 'quantity' => 2]];
        
        // Act
        $result = app(InvoiceService::class)->calculateTax($items);
        
        // Assert
        $this->assertEquals(10000, $result['tax_amount']); // 5% VAT
        $this->assertEquals(210000, $result['total_amount']);
    }
}
```

### 3. API Feature Tests
```php
class DrugApiTest extends ApiTestCase
{
    /** @test */
    public function authenticated_user_can_view_drugs()
    {
        // Arrange
        $this->authenticateAs('pharmacist');
        Drug::factory()->count(3)->create();
        
        // Act
        $response = $this->getJson('/api/drugs');
        
        // Assert
        $response->assertOk()
                ->assertJsonCount(3, 'data')
                ->assertJsonStructure([
                    'data' => [
                        '*' => ['id', 'code', 'name', 'price']
                    ]
                ]);
    }
    
    /** @test */
    public function unauthorized_user_cannot_create_drug()
    {
        // Act
        $response = $this->postJson('/api/drugs', [
            'name' => 'Test Drug',
            'code' => 'TEST001'
        ]);
        
        // Assert
        $response->assertUnauthorized();
    }
}
```

### 4. Sales Process Tests
```php
class SalesProcessTest extends ApiTestCase
{
    /** @test */
    public function complete_sales_process_works_correctly()
    {
        // Arrange
        $this->authenticateAs('pharmacist');
        $customer = Customer::factory()->create();
        $drug = Drug::factory()->create(['price' => 50000]);
        $batch = Batch::factory()->create([
            'drug_id' => $drug->id,
            'current_quantity' => 100
        ]);
        
        // Act - Create invoice
        $response = $this->postJson('/api/invoices', [
            'customer_id' => $customer->id,
            'items' => [
                [
                    'batch_id' => $batch->id,
                    'quantity' => 2,
                    'unit_price' => 50000
                ]
            ]
        ]);
        
        // Assert
        $response->assertCreated();
        
        // Verify stock reduction
        $this->assertEquals(98, $batch->fresh()->current_quantity);
        
        // Verify invoice creation
        $this->assertDatabaseHas('invoices', [
            'customer_id' => $customer->id,
            'total_amount' => 110000 // Including 10% VAT
        ]);
    }
}
```

## Test Data Management

### 1. Seeders for Testing
```php
class TestSeeder extends Seeder
{
    public function run(): void
    {
        // Create basic roles and permissions
        Role::create(['name' => 'super_admin']);
        Role::create(['name' => 'pharmacist']);
        Role::create(['name' => 'cashier']);
        
        // Create test user
        $admin = User::factory()->create([
            'username' => 'admin',
            'email' => '<EMAIL>'
        ]);
        $admin->assignRole('super_admin');
    }
}
```

### 2. Test Traits
```php
trait CreatesTestData
{
    protected function createDrugWithBatch(array $drugData = [], array $batchData = []): array
    {
        $drug = Drug::factory()->create($drugData);
        $batch = Batch::factory()->create(array_merge([
            'drug_id' => $drug->id
        ], $batchData));
        
        return compact('drug', 'batch');
    }
    
    protected function createInvoiceWithItems(Customer $customer, array $items): Invoice
    {
        // Helper method to create complete invoice
    }
}
```

## Performance Tests
```php
class PerformanceTest extends TestCase
{
    /** @test */
    public function drug_search_performs_well_with_large_dataset()
    {
        // Create 10,000 drugs
        Drug::factory()->count(10000)->create();
        
        $startTime = microtime(true);
        
        app(DrugService::class)->searchDrugs('test');
        
        $executionTime = microtime(true) - $startTime;
        
        $this->assertLessThan(1.0, $executionTime, 'Search should complete within 1 second');
    }
}
```

## Test Commands
```bash
# Run all tests
php artisan test

# Run specific test suite
php artisan test --testsuite=Feature
php artisan test --testsuite=Unit

# Run with coverage
php artisan test --coverage

# Run specific test file
php artisan test tests/Feature/DrugApiTest.php

# Run tests in parallel
php artisan test --parallel
```

## Câu lệnh để bắt đầu
"Hãy tạo test suite hoàn chỉnh cho DrugService với unit tests và feature tests cho API endpoints"
