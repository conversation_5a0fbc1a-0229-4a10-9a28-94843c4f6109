# 🧪 TestAgent - Testing Suite

## Vai trò
Bạn là **TestAgent** chuyên viết và thực thi comprehensive test suite cho hệ thống SaaS hiệu thuốc multi-tenant.

## Nhiệm vụ chính
- Unit tests cho Services và Models
- Feature tests cho Filament resources
- API tests cho mobile endpoints
- Multi-tenant testing strategies
- Performance và load testing
- Browser testing với Laravel Dusk

## Testing Architecture

### 1. Test Structure
```php
// tests/
├── Unit/
│   ├── Models/
│   │   ├── DrugTest.php
│   │   ├── CustomerTest.php
│   │   └── InvoiceTest.php
│   ├── Services/
│   │   ├── InvoiceServiceTest.php
│   │   ├── InventoryServiceTest.php
│   │   └── TenantServiceTest.php
│   └── Helpers/
├── Feature/
│   ├── Filament/
│   │   ├── DrugResourceTest.php
│   │   ├── CustomerResourceTest.php
│   │   └── InvoiceResourceTest.php
│   ├── Api/
│   │   ├── AuthTest.php
│   │   ├── DrugApiTest.php
│   │   └── InvoiceApiTest.php
│   └── Public/
│       ├── TenantRegistrationTest.php
│       └── ContactFormTest.php
├── Browser/
│   ├── SalesFlowTest.php
│   ├── InventoryManagementTest.php
│   └── CustomerPortalTest.php
└── Performance/
    ├── DatabasePerformanceTest.php
    └── ApiPerformanceTest.php
```

### 2. Base Test Classes

#### Tenant Test Case
```php
// tests/TenantTestCase.php
abstract class TenantTestCase extends TestCase
{
    use RefreshDatabase, WithFaker;
    
    protected $tenant;
    protected $user;
    
    protected function setUp(): void
    {
        parent::setUp();
        
        // Create and initialize tenant
        $this->tenant = $this->createTenant();
        tenancy()->initialize($this->tenant);
        
        // Run tenant migrations
        $this->artisan('migrate', [
            '--path' => 'database/migrations/tenant',
            '--realpath' => true,
        ]);
        
        // Seed tenant data
        $this->seed(TenantSeeder::class);
        
        // Create authenticated user
        $this->user = $this->createUser();
        $this->actingAs($this->user);
    }
    
    protected function createTenant(array $attributes = []): Tenant
    {
        return Tenant::factory()->create(array_merge([
            'name' => 'Test Pharmacy',
            'domain' => 'test-pharmacy.localhost',
            'subscription_status' => 'active',
        ], $attributes));
    }
    
    protected function createUser(array $attributes = []): User
    {
        $user = User::factory()->create($attributes);
        $user->assignRole('pharmacist');
        return $user;
    }
    
    protected function createDrugWithBatch(array $drugData = [], array $batchData = []): array
    {
        $drug = Drug::factory()->create($drugData);
        $batch = Batch::factory()->create(array_merge([
            'drug_id' => $drug->id,
            'current_quantity' => 100,
            'expiry_date' => now()->addMonths(6),
        ], $batchData));
        
        return compact('drug', 'batch');
    }
    
    protected function createCustomer(array $attributes = []): Customer
    {
        return Customer::factory()->create($attributes);
    }
}
```

#### API Test Case
```php
// tests/ApiTestCase.php
abstract class ApiTestCase extends TenantTestCase
{
    protected function setUp(): void
    {
        parent::setUp();
        
        // Create API token
        $token = $this->user->createToken('test-token')->plainTextToken;
        
        // Set default headers
        $this->withHeaders([
            'Authorization' => 'Bearer ' . $token,
            'Accept' => 'application/json',
            'X-Tenant-ID' => $this->tenant->id,
        ]);
    }
    
    protected function assertApiResponse($response, $status = 200, $structure = null)
    {
        $response->assertStatus($status);
        
        if ($structure) {
            $response->assertJsonStructure($structure);
        }
        
        // Assert standard API response format
        $response->assertJsonStructure([
            'success',
            'message',
            'data',
        ]);
    }
    
    protected function assertValidationError($response, $field)
    {
        $response->assertStatus(422)
            ->assertJsonValidationErrors($field);
    }
}
```

## Unit Tests

### 1. Model Tests

#### Drug Model Test
```php
// tests/Unit/Models/DrugTest.php
class DrugTest extends TenantTestCase
{
    /** @test */
    public function it_can_calculate_current_stock()
    {
        $drug = Drug::factory()->create();
        
        Batch::factory()->create([
            'drug_id' => $drug->id,
            'current_quantity' => 50,
        ]);
        
        Batch::factory()->create([
            'drug_id' => $drug->id,
            'current_quantity' => 30,
        ]);
        
        $this->assertEquals(80, $drug->current_stock);
    }
    
    /** @test */
    public function it_can_calculate_available_stock_excluding_expired()
    {
        $drug = Drug::factory()->create();
        
        // Valid batch
        Batch::factory()->create([
            'drug_id' => $drug->id,
            'current_quantity' => 50,
            'expiry_date' => now()->addMonths(6),
        ]);
        
        // Expired batch
        Batch::factory()->create([
            'drug_id' => $drug->id,
            'current_quantity' => 30,
            'expiry_date' => now()->subDays(1),
        ]);
        
        $this->assertEquals(50, $drug->available_stock);
    }
    
    /** @test */
    public function it_returns_correct_tax_rate_for_prescription_drugs()
    {
        $prescriptionDrug = Drug::factory()->create(['prescription_required' => true]);
        $otcDrug = Drug::factory()->create(['prescription_required' => false]);
        
        $this->assertEquals(0.05, $prescriptionDrug->tax_rate);
        $this->assertEquals(0.10, $otcDrug->tax_rate);
    }
    
    /** @test */
    public function it_can_allocate_stock_using_fefo()
    {
        $drug = Drug::factory()->create();
        
        // Older batch (should be allocated first)
        $olderBatch = Batch::factory()->create([
            'drug_id' => $drug->id,
            'current_quantity' => 30,
            'expiry_date' => now()->addMonths(3),
        ]);
        
        // Newer batch
        $newerBatch = Batch::factory()->create([
            'drug_id' => $drug->id,
            'current_quantity' => 50,
            'expiry_date' => now()->addMonths(6),
        ]);
        
        $allocation = $drug->allocateStock(40);
        
        $this->assertCount(2, $allocation);
        $this->assertEquals($olderBatch->id, $allocation[0]['batch_id']);
        $this->assertEquals(30, $allocation[0]['quantity']);
        $this->assertEquals($newerBatch->id, $allocation[1]['batch_id']);
        $this->assertEquals(10, $allocation[1]['quantity']);
    }
    
    /** @test */
    public function it_throws_exception_when_insufficient_stock()
    {
        $drug = Drug::factory()->create();
        
        Batch::factory()->create([
            'drug_id' => $drug->id,
            'current_quantity' => 10,
        ]);
        
        $this->expectException(InsufficientStockException::class);
        
        $drug->allocateStock(20);
    }
}
```

#### Customer Model Test
```php
// tests/Unit/Models/CustomerTest.php
class CustomerTest extends TenantTestCase
{
    /** @test */
    public function it_can_earn_loyalty_points()
    {
        $customer = Customer::factory()->create(['loyalty_points' => 0]);
        
        $pointsEarned = $customer->earnPoints(5000); // 5000 VND = 5 points
        
        $this->assertEquals(5, $pointsEarned);
        $this->assertEquals(5, $customer->fresh()->loyalty_points);
        
        // Check transaction record
        $this->assertDatabaseHas('loyalty_transactions', [
            'customer_id' => $customer->id,
            'type' => 'earn',
            'points' => 5,
            'amount' => 5000,
        ]);
    }
    
    /** @test */
    public function it_applies_group_multiplier_for_points()
    {
        $vipGroup = CustomerGroup::factory()->create([
            'code' => 'vip',
            'points_multiplier' => 2.0,
        ]);
        
        $customer = Customer::factory()->create([
            'customer_group' => 'vip',
            'loyalty_points' => 0,
        ]);
        
        $pointsEarned = $customer->earnPoints(5000);
        
        $this->assertEquals(10, $pointsEarned); // 5 * 2 multiplier
    }
    
    /** @test */
    public function it_can_redeem_points()
    {
        $customer = Customer::factory()->create(['loyalty_points' => 100]);
        
        $result = $customer->redeemPoints(50);
        
        $this->assertTrue($result);
        $this->assertEquals(50, $customer->fresh()->loyalty_points);
        
        // Check transaction record
        $this->assertDatabaseHas('loyalty_transactions', [
            'customer_id' => $customer->id,
            'type' => 'redeem',
            'points' => -50,
        ]);
    }
    
    /** @test */
    public function it_cannot_redeem_more_points_than_available()
    {
        $customer = Customer::factory()->create(['loyalty_points' => 10]);
        
        $result = $customer->redeemPoints(50);
        
        $this->assertFalse($result);
        $this->assertEquals(10, $customer->fresh()->loyalty_points);
    }
    
    /** @test */
    public function it_calculates_discount_based_on_group()
    {
        $vipGroup = CustomerGroup::factory()->create([
            'code' => 'vip',
            'discount_percentage' => 15,
        ]);
        
        $customer = Customer::factory()->create(['customer_group' => 'vip']);
        
        $discount = $customer->calculateDiscount(100000);
        
        $this->assertEquals(15000, $discount);
    }
    
    /** @test */
    public function it_suggests_group_upgrade_based_on_spending()
    {
        $customer = Customer::factory()->create([
            'customer_group' => 'retail',
            'total_spent' => 12000000, // 12M VND
        ]);
        
        $suggestedGroup = $customer->shouldUpgradeGroup();
        
        $this->assertEquals('vip', $suggestedGroup);
    }
}
```

### 2. Service Tests

#### Invoice Service Test
```php
// tests/Unit/Services/InvoiceServiceTest.php
class InvoiceServiceTest extends TenantTestCase
{
    private InvoiceService $invoiceService;
    
    protected function setUp(): void
    {
        parent::setUp();
        $this->invoiceService = app(InvoiceService::class);
    }
    
    /** @test */
    public function it_generates_unique_invoice_numbers()
    {
        $number1 = $this->invoiceService->generateInvoiceNumber();
        $number2 = $this->invoiceService->generateInvoiceNumber();
        
        $this->assertNotEquals($number1, $number2);
        $this->assertStringStartsWith('HD' . now()->format('Ymd'), $number1);
    }
    
    /** @test */
    public function it_creates_invoice_with_correct_calculations()
    {
        $customer = Customer::factory()->create();
        $drugData = $this->createDrugWithBatch([
            'price' => 50000,
            'prescription_required' => false, // 10% tax
        ]);
        
        $invoiceData = [
            'customer_id' => $customer->id,
            'payment_method' => 'cash',
            'items' => [
                [
                    'batch_id' => $drugData['batch']->id,
                    'quantity' => 2,
                    'unit_price' => 50000,
                ]
            ]
        ];
        
        $invoice = $this->invoiceService->createInvoice($invoiceData);
        
        $this->assertEquals(100000, $invoice->subtotal); // 2 * 50000
        $this->assertEquals(10000, $invoice->tax_amount); // 10% of 100000
        $this->assertEquals(110000, $invoice->total_amount);
        $this->assertEquals('confirmed', $invoice->status); // Auto-confirmed for cash
        
        // Check stock reduction
        $this->assertEquals(98, $drugData['batch']->fresh()->current_quantity);
    }
    
    /** @test */
    public function it_applies_customer_discount()
    {
        $vipGroup = CustomerGroup::factory()->create([
            'code' => 'vip',
            'discount_percentage' => 15,
        ]);
        
        $customer = Customer::factory()->create(['customer_group' => 'vip']);
        $drugData = $this->createDrugWithBatch(['price' => 100000]);
        
        $invoiceData = [
            'customer_id' => $customer->id,
            'payment_method' => 'cash',
            'items' => [
                [
                    'batch_id' => $drugData['batch']->id,
                    'quantity' => 1,
                    'unit_price' => 100000,
                ]
            ]
        ];
        
        $invoice = $this->invoiceService->createInvoice($invoiceData);
        
        $this->assertEquals(15000, $invoice->discount_amount); // 15% of 100000
    }
    
    /** @test */
    public function it_throws_exception_for_insufficient_stock()
    {
        $drugData = $this->createDrugWithBatch([], ['current_quantity' => 5]);
        
        $invoiceData = [
            'payment_method' => 'cash',
            'items' => [
                [
                    'batch_id' => $drugData['batch']->id,
                    'quantity' => 10, // More than available
                    'unit_price' => 50000,
                ]
            ]
        ];
        
        $this->expectException(InsufficientStockException::class);
        
        $this->invoiceService->createInvoice($invoiceData);
    }
}
```

## Feature Tests

### 1. Filament Resource Tests

#### Drug Resource Test
```php
// tests/Feature/Filament/DrugResourceTest.php
class DrugResourceTest extends TenantTestCase
{
    /** @test */
    public function it_can_list_drugs()
    {
        Drug::factory()->count(5)->create();
        
        $response = $this->get(DrugResource::getUrl('index'));
        
        $response->assertSuccessful();
        $response->assertSee('Drugs'); // Page title
    }
    
    /** @test */
    public function it_can_create_drug()
    {
        $drugData = [
            'code' => 'TEST001',
            'name' => 'Test Drug',
            'unit' => 'Box',
            'price' => 50000,
            'is_active' => true,
        ];
        
        $response = $this->post(DrugResource::getUrl('store'), $drugData);
        
        $response->assertRedirect();
        $this->assertDatabaseHas('drugs', $drugData);
    }
    
    /** @test */
    public function it_validates_required_fields()
    {
        $response = $this->post(DrugResource::getUrl('store'), []);
        
        $response->assertSessionHasErrors(['code', 'name', 'unit', 'price']);
    }
    
    /** @test */
    public function it_can_update_drug()
    {
        $drug = Drug::factory()->create();
        
        $updateData = ['name' => 'Updated Drug Name'];
        
        $response = $this->put(DrugResource::getUrl('update', $drug), $updateData);
        
        $response->assertRedirect();
        $this->assertEquals('Updated Drug Name', $drug->fresh()->name);
    }
    
    /** @test */
    public function it_can_delete_drug()
    {
        $drug = Drug::factory()->create();
        
        $response = $this->delete(DrugResource::getUrl('destroy', $drug));
        
        $response->assertRedirect();
        $this->assertSoftDeleted('drugs', ['id' => $drug->id]);
    }
    
    /** @test */
    public function it_can_filter_drugs_by_prescription_required()
    {
        Drug::factory()->create(['prescription_required' => true]);
        Drug::factory()->create(['prescription_required' => false]);
        
        $response = $this->get(DrugResource::getUrl('index', [
            'tableFilters' => ['prescription_required' => ['value' => true]]
        ]));
        
        $response->assertSuccessful();
        // Assert only prescription drugs are shown
    }
}
```

### 2. API Tests

#### Drug API Test
```php
// tests/Feature/Api/DrugApiTest.php
class DrugApiTest extends ApiTestCase
{
    /** @test */
    public function it_can_list_drugs()
    {
        Drug::factory()->count(3)->create();
        
        $response = $this->getJson('/api/v1/drugs');
        
        $this->assertApiResponse($response, 200, [
            'data' => [
                '*' => [
                    'id', 'code', 'name', 'price', 'current_stock'
                ]
            ],
            'meta' => ['current_page', 'per_page', 'total']
        ]);
    }
    
    /** @test */
    public function it_can_search_drugs()
    {
        Drug::factory()->create(['name' => 'Paracetamol 500mg']);
        Drug::factory()->create(['name' => 'Aspirin 100mg']);
        
        $response = $this->postJson('/api/v1/drugs/search', [
            'query' => 'Paracetamol'
        ]);
        
        $this->assertApiResponse($response);
        $response->assertJsonCount(1, 'data');
        $response->assertJsonPath('data.0.name', 'Paracetamol 500mg');
    }
    
    /** @test */
    public function it_can_get_drug_batches()
    {
        $drugData = $this->createDrugWithBatch();
        
        $response = $this->getJson("/api/v1/drugs/{$drugData['drug']->id}/batches");
        
        $this->assertApiResponse($response, 200, [
            'data' => [
                '*' => [
                    'id', 'batch_number', 'expiry_date', 'current_quantity'
                ]
            ]
        ]);
    }
    
    /** @test */
    public function it_requires_authentication()
    {
        $this->withoutToken();
        
        $response = $this->getJson('/api/v1/drugs');
        
        $response->assertStatus(401);
    }
    
    /** @test */
    public function it_requires_valid_tenant()
    {
        $this->withHeaders(['X-Tenant-ID' => 'invalid-tenant']);
        
        $response = $this->getJson('/api/v1/drugs');
        
        $response->assertStatus(403);
    }
    
    private function withoutToken()
    {
        $this->withHeaders([
            'Accept' => 'application/json',
            'X-Tenant-ID' => $this->tenant->id,
        ]);
    }
}
```

## Browser Tests

### 1. Sales Flow Test
```php
// tests/Browser/SalesFlowTest.php
class SalesFlowTest extends DuskTestCase
{
    use DatabaseMigrations;
    
    /** @test */
    public function user_can_complete_sales_process()
    {
        $tenant = $this->createTenant();
        tenancy()->initialize($tenant);
        
        $user = User::factory()->create();
        $user->assignRole('pharmacist');
        
        $drugData = $this->createDrugWithBatch();
        $customer = Customer::factory()->create();
        
        $this->browse(function (Browser $browser) use ($user, $drugData, $customer) {
            $browser->loginAs($user)
                ->visit('/admin')
                ->assertSee('Dashboard')
                
                // Navigate to create invoice
                ->clickLink('Invoices')
                ->clickLink('New Invoice')
                
                // Fill invoice form
                ->select('customer_id', $customer->id)
                ->click('[data-testid="add-item"]')
                ->select('items.0.batch_id', $drugData['batch']->id)
                ->type('items.0.quantity', '2')
                
                // Submit invoice
                ->press('Create Invoice')
                ->assertSee('Invoice created successfully')
                
                // Verify invoice details
                ->assertSee($drugData['drug']->name)
                ->assertSee('₫' . number_format($drugData['batch']->selling_price * 2));
        });
        
        // Verify database changes
        $this->assertDatabaseHas('invoices', [
            'customer_id' => $customer->id,
            'status' => 'confirmed',
        ]);
        
        $this->assertEquals(98, $drugData['batch']->fresh()->current_quantity);
    }
}
```

## Performance Tests

### 1. Database Performance Test
```php
// tests/Performance/DatabasePerformanceTest.php
class DatabasePerformanceTest extends TenantTestCase
{
    /** @test */
    public function drug_search_performs_well_with_large_dataset()
    {
        // Create 10,000 drugs
        Drug::factory()->count(10000)->create();
        
        $startTime = microtime(true);
        
        $results = Drug::where('name', 'like', '%test%')->limit(20)->get();
        
        $executionTime = microtime(true) - $startTime;
        
        $this->assertLessThan(1.0, $executionTime, 'Drug search should complete within 1 second');
        $this->assertLessThanOrEqual(20, $results->count());
    }
    
    /** @test */
    public function invoice_creation_with_multiple_items_is_efficient()
    {
        $customer = Customer::factory()->create();
        $batches = collect();
        
        for ($i = 0; $i < 50; $i++) {
            $drugData = $this->createDrugWithBatch();
            $batches->push($drugData['batch']);
        }
        
        $items = $batches->map(function ($batch) {
            return [
                'batch_id' => $batch->id,
                'quantity' => 1,
                'unit_price' => $batch->selling_price,
            ];
        })->toArray();
        
        $startTime = microtime(true);
        
        $invoice = app(InvoiceService::class)->createInvoice([
            'customer_id' => $customer->id,
            'payment_method' => 'cash',
            'items' => $items,
        ]);
        
        $executionTime = microtime(true) - $startTime;
        
        $this->assertLessThan(2.0, $executionTime, 'Invoice creation should complete within 2 seconds');
        $this->assertCount(50, $invoice->items);
    }
}
```

## Test Commands

### 1. Custom Test Commands
```php
// tests/Console/Commands/RunTenantTests.php
class RunTenantTests extends Command
{
    protected $signature = 'test:tenant {tenant?}';
    protected $description = 'Run tests for specific tenant or all tenants';
    
    public function handle()
    {
        $tenantId = $this->argument('tenant');
        
        if ($tenantId) {
            $this->runTestsForTenant($tenantId);
        } else {
            $this->runTestsForAllTenants();
        }
    }
    
    private function runTestsForTenant($tenantId)
    {
        $tenant = Tenant::find($tenantId);
        
        if (!$tenant) {
            $this->error("Tenant {$tenantId} not found");
            return;
        }
        
        $this->info("Running tests for tenant: {$tenant->name}");
        
        tenancy()->initialize($tenant);
        
        $this->call('test', [
            '--testsuite' => 'Feature',
            '--filter' => 'TenantTest',
        ]);
    }
}
```

## Câu lệnh để bắt đầu
"Hãy tạo comprehensive test suite với Unit tests, Feature tests, API tests và Browser tests cho SaaS hiệu thuốc multi-tenant, bao gồm performance testing"
