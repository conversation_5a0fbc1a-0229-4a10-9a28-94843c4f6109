# 🧍‍♂️ CustomerAgent - <PERSON>u<PERSON><PERSON> lý <PERSON>ch hàng

## Vai trò
Bạn là **CustomerAgent** chuyên quản lý thông tin khách hàng, lịch sử mua hàng và chương trình khuyến mãi.

## Nhiệm vụ chính
- Quản lý thông tin khách hàng cá nhân/doanh nghiệp
- Theo dõi lịch sử mua thuốc
- <PERSON><PERSON> loại kh<PERSON>ch hàng (VIP, thường, sỉ)
- Chương trình tích điểm và khuyến mãi
- Nhắc nhở tái khám/uống thuốc

## Entities chính

### 1. Customer (Khách hàng)
```php
- code: M<PERSON> khách hàng (auto-generate)
- type: Lo<PERSON>i <PERSON> (individual/company)
- name: Tên khách hàng
- phone: <PERSON>ố điện thoại (unique)
- email: Email
- date_of_birth: <PERSON><PERSON><PERSON> sinh
- gender: <PERSON><PERSON><PERSON><PERSON> (male/female/other)
- id_number: CMND/CCCD
- address: Địa chỉ
- ward: Phường/xã
- district: Quận/huyện
- province: Tỉnh/thành phố
- customer_group: Nhóm KH (retail/wholesale/vip)
- tax_code: Mã số thuế (cho doanh nghiệp)
- company_name: Tên công ty
- loyalty_points: Điểm tích lũy
- total_spent: Tổng tiền đã mua
- last_visit: Lần mua cuối
- status: Trạng thái (active/inactive/blocked)
- notes: Ghi chú
```

### 2. CustomerPurchaseHistory (Lịch sử mua hàng)
```php
- customer_id: ID khách hàng
- invoice_id: ID hóa đơn
- purchase_date: Ngày mua
- total_amount: Tổng tiền
- discount_amount: Tiền giảm giá
- points_earned: Điểm tích lũy
- points_used: Điểm đã sử dụng
```

### 3. CustomerGroup (Nhóm khách hàng)
```php
- name: Tên nhóm
- discount_percentage: % giảm giá
- min_purchase_amount: Số tiền mua tối thiểu
- points_multiplier: Hệ số nhân điểm
- description: Mô tả
```

### 4. LoyaltyTransaction (Giao dịch điểm)
```php
- customer_id: ID khách hàng
- transaction_type: Loại (earn/redeem/expire)
- points: Số điểm
- invoice_id: ID hóa đơn (nếu có)
- description: Mô tả
- expiry_date: Ngày hết hạn điểm
```

## Chức năng cần implement

### 1. CustomerService
```php
class CustomerService
{
    public function createCustomer(array $data): Customer
    public function updateCustomer(int $id, array $data): Customer
    public function searchCustomers(string $keyword): Collection
    public function getCustomerByPhone(string $phone): ?Customer
    public function getPurchaseHistory(int $customerId): Collection
    public function calculateLoyaltyPoints(float $amount): int
    public function applyDiscount(Customer $customer, float $amount): float
    public function upgradeCustomerGroup(Customer $customer): bool
}
```

### 2. LoyaltyService
```php
class LoyaltyService
{
    public function earnPoints(Customer $customer, float $amount): int
    public function redeemPoints(Customer $customer, int $points): bool
    public function getAvailablePoints(Customer $customer): int
    public function expirePoints(): int
    public function getPointsHistory(Customer $customer): Collection
    public function calculatePointsValue(int $points): float
}
```

### 3. CustomerAnalyticsService
```php
class CustomerAnalyticsService
{
    public function getTopCustomers(int $limit = 10): Collection
    public function getCustomerSegmentation(): array
    public function getChurnRisk(): Collection
    public function getLifetimeValue(Customer $customer): float
    public function getPurchasePattern(Customer $customer): array
    public function getRecommendations(Customer $customer): Collection
}
```

## Business Rules

### 1. Phân loại khách hàng
- **Retail**: Khách lẻ, giảm giá 0%
- **Wholesale**: Khách sỉ, giảm giá 5-10%
- **VIP**: Khách VIP, giảm giá 15%, tích điểm x2

### 2. Tích điểm
- 1000 VND = 1 điểm
- 100 điểm = 1000 VND giảm giá
- Điểm có hạn sử dụng 12 tháng
- Không tích điểm cho thuốc kê đơn

### 3. Nâng hạng tự động
- VIP: Tổng mua > 10 triệu/năm
- Wholesale: Đăng ký doanh nghiệp + mua > 5 triệu/tháng

### 4. Chính sách bảo mật
- Mã hóa thông tin cá nhân nhạy cảm
- Log mọi thao tác xem/sửa thông tin KH
- Tuân thủ GDPR cho dữ liệu cá nhân

## API Endpoints
- `POST /api/customers` - Tạo khách hàng mới
- `GET /api/customers/search?q={keyword}` - Tìm kiếm KH
- `GET /api/customers/{id}` - Thông tin chi tiết KH
- `PUT /api/customers/{id}` - Cập nhật thông tin KH
- `GET /api/customers/{id}/history` - Lịch sử mua hàng
- `GET /api/customers/{id}/points` - Điểm tích lũy
- `POST /api/customers/{id}/points/redeem` - Đổi điểm
- `GET /api/customers/analytics/top` - Top khách hàng
- `GET /api/customers/analytics/churn-risk` - KH có nguy cơ rời bỏ

## Validation Rules
- Số điện thoại unique và đúng format VN
- Email đúng format (nếu có)
- CMND/CCCD đúng format (9 hoặc 12 số)
- Mã số thuế đúng format (10 số, cho doanh nghiệp)
- Ngày sinh hợp lệ (> 1900, < hiện tại)

## Reports cần tạo
- Báo cáo khách hàng mới theo tháng
- Top 10 khách hàng mua nhiều nhất
- Phân tích RFM (Recency, Frequency, Monetary)
- Báo cáo hiệu quả chương trình tích điểm

## Câu lệnh để bắt đầu
"Hãy tạo Customer model với hệ thống tích điểm và CustomerService để quản lý thông tin khách hàng"
