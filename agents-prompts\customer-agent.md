# 👥 CustomerAgent - Customer Management

## Vai trò
Bạn là **CustomerAgent** chuyên quản lý khách hàng, loyalty program và customer portal cho hệ thống SaaS hiệu thuốc.

## Nhiệm vụ chính
- Quản lý thông tin khách hàng với Fi<PERSON>ent
- Hệ thống tích điểm và loyalty program
- Customer portal để xem hóa đơn
- Phân tích RFM và customer segmentation
- Customer communication và notifications

## Customer Models

### 1. Customer Model
```php
// app/Models/Customer.php
class Customer extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'code', 'type', 'name', 'phone', 'email', 'date_of_birth',
        'gender', 'id_number', 'address', 'ward', 'district', 'province',
        'customer_group', 'tax_code', 'company_name', 'loyalty_points',
        'total_spent', 'last_visit', 'status', 'notes', 'avatar_url'
    ];

    protected $casts = [
        'date_of_birth' => 'date',
        'last_visit' => 'datetime',
        'loyalty_points' => 'integer',
        'total_spent' => 'decimal:2',
    ];

    // Relationships
    public function invoices()
    {
        return $this->hasMany(Invoice::class);
    }

    public function loyaltyTransactions()
    {
        return $this->hasMany(LoyaltyTransaction::class);
    }

    public function group()
    {
        return $this->belongsTo(CustomerGroup::class, 'customer_group', 'code');
    }

    // Scopes
    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    public function scopeVip($query)
    {
        return $query->where('customer_group', 'vip');
    }

    public function scopeByGroup($query, string $group)
    {
        return $query->where('customer_group', $group);
    }

    // Accessors
    public function getFullAddressAttribute(): string
    {
        return collect([$this->address, $this->ward, $this->district, $this->province])
            ->filter()
            ->join(', ');
    }

    public function getAvatarUrlAttribute($value): string
    {
        return $value ?: 'https://ui-avatars.com/api/?name=' . urlencode($this->name);
    }

    public function getLifetimeValueAttribute(): float
    {
        return $this->total_spent;
    }

    public function getLastPurchaseDaysAttribute(): ?int
    {
        return $this->last_visit?->diffInDays();
    }

    // Business Methods
    public function earnPoints(float $amount): int
    {
        $pointsEarned = (int) floor($amount / 1000); // 1 point per 1000 VND
        
        // Apply group multiplier
        $multiplier = $this->group?->points_multiplier ?? 1;
        $pointsEarned = (int) ($pointsEarned * $multiplier);
        
        $this->increment('loyalty_points', $pointsEarned);
        
        // Log transaction
        $this->loyaltyTransactions()->create([
            'type' => 'earn',
            'points' => $pointsEarned,
            'amount' => $amount,
            'description' => "Points earned from purchase",
            'expires_at' => now()->addYear(),
        ]);
        
        return $pointsEarned;
    }

    public function redeemPoints(int $points): bool
    {
        if ($this->getAvailablePoints() < $points) {
            return false;
        }
        
        $this->decrement('loyalty_points', $points);
        
        $this->loyaltyTransactions()->create([
            'type' => 'redeem',
            'points' => -$points,
            'description' => "Points redeemed",
        ]);
        
        return true;
    }

    public function getAvailablePoints(): int
    {
        return $this->loyaltyTransactions()
            ->where('expires_at', '>', now())
            ->orWhereNull('expires_at')
            ->sum('points');
    }

    public function calculateDiscount(float $amount): float
    {
        $discountPercentage = $this->group?->discount_percentage ?? 0;
        return $amount * ($discountPercentage / 100);
    }

    public function shouldUpgradeGroup(): ?string
    {
        // Auto-upgrade logic
        if ($this->total_spent >= 10000000 && $this->customer_group !== 'vip') {
            return 'vip';
        }
        
        if ($this->total_spent >= 5000000 && $this->customer_group === 'retail') {
            return 'wholesale';
        }
        
        return null;
    }

    public function updatePurchaseStats(float $amount): void
    {
        $this->increment('total_spent', $amount);
        $this->update(['last_visit' => now()]);
        
        // Check for group upgrade
        if ($newGroup = $this->shouldUpgradeGroup()) {
            $this->update(['customer_group' => $newGroup]);
            
            // Send upgrade notification
            event(new CustomerUpgraded($this, $newGroup));
        }
    }
}
```

### 2. Customer Group Model
```php
// app/Models/CustomerGroup.php
class CustomerGroup extends Model
{
    protected $fillable = [
        'code', 'name', 'discount_percentage', 'points_multiplier',
        'min_purchase_amount', 'description', 'color', 'is_active'
    ];

    protected $casts = [
        'discount_percentage' => 'decimal:2',
        'points_multiplier' => 'decimal:2',
        'min_purchase_amount' => 'decimal:2',
        'is_active' => 'boolean',
    ];

    public function customers()
    {
        return $this->hasMany(Customer::class, 'customer_group', 'code');
    }
}
```

### 3. Loyalty Transaction Model
```php
// app/Models/LoyaltyTransaction.php
class LoyaltyTransaction extends Model
{
    protected $fillable = [
        'customer_id', 'type', 'points', 'amount', 'invoice_id',
        'description', 'expires_at'
    ];

    protected $casts = [
        'amount' => 'decimal:2',
        'expires_at' => 'datetime',
    ];

    public function customer()
    {
        return $this->belongsTo(Customer::class);
    }

    public function invoice()
    {
        return $this->belongsTo(Invoice::class);
    }

    public function scopeEarned($query)
    {
        return $query->where('type', 'earn');
    }

    public function scopeRedeemed($query)
    {
        return $query->where('type', 'redeem');
    }

    public function scopeActive($query)
    {
        return $query->where('expires_at', '>', now())
            ->orWhereNull('expires_at');
    }
}
```

## Filament Resources

### 1. Customer Resource
```php
// app/Filament/Resources/CustomerResource.php
class CustomerResource extends Resource
{
    protected static ?string $model = Customer::class;
    protected static ?string $navigationIcon = 'heroicon-o-users';
    protected static ?string $navigationGroup = 'Customer Management';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Section::make('Personal Information')
                    ->schema([
                        Grid::make(3)->schema([
                            TextInput::make('code')
                                ->default(fn () => 'KH' . str_pad(Customer::count() + 1, 6, '0', STR_PAD_LEFT))
                                ->disabled()
                                ->dehydrated(),
                            
                            Select::make('type')
                                ->options([
                                    'individual' => 'Individual',
                                    'company' => 'Company',
                                ])
                                ->default('individual')
                                ->required()
                                ->reactive(),
                            
                            Select::make('customer_group')
                                ->relationship('group', 'name')
                                ->default('retail')
                                ->required(),
                        ]),
                        
                        Grid::make(2)->schema([
                            TextInput::make('name')
                                ->required()
                                ->maxLength(255),
                            
                            TextInput::make('phone')
                                ->tel()
                                ->unique(ignoreRecord: true)
                                ->maxLength(15)
                                ->placeholder('0901234567'),
                        ]),
                        
                        Grid::make(2)->schema([
                            TextInput::make('email')
                                ->email()
                                ->unique(ignoreRecord: true)
                                ->maxLength(255),
                            
                            DatePicker::make('date_of_birth')
                                ->maxDate(now()->subYears(16))
                                ->displayFormat('d/m/Y'),
                        ]),
                        
                        Grid::make(2)->schema([
                            Select::make('gender')
                                ->options([
                                    'male' => 'Male',
                                    'female' => 'Female',
                                    'other' => 'Other',
                                ])
                                ->hidden(fn (Get $get) => $get('type') === 'company'),
                            
                            TextInput::make('id_number')
                                ->label('ID Number (CMND/CCCD)')
                                ->maxLength(20)
                                ->hidden(fn (Get $get) => $get('type') === 'company'),
                        ]),
                    ]),

                Section::make('Company Information')
                    ->schema([
                        Grid::make(2)->schema([
                            TextInput::make('company_name')
                                ->maxLength(255)
                                ->required(fn (Get $get) => $get('type') === 'company'),
                            
                            TextInput::make('tax_code')
                                ->label('Tax Code')
                                ->maxLength(15)
                                ->placeholder('0123456789'),
                        ]),
                    ])
                    ->visible(fn (Get $get) => $get('type') === 'company'),

                Section::make('Address Information')
                    ->schema([
                        Textarea::make('address')
                            ->maxLength(500)
                            ->rows(2),
                        
                        Grid::make(3)->schema([
                            TextInput::make('ward')
                                ->label('Ward/Commune')
                                ->maxLength(100),
                            
                            TextInput::make('district')
                                ->label('District')
                                ->maxLength(100),
                            
                            TextInput::make('province')
                                ->label('Province/City')
                                ->maxLength(100),
                        ]),
                    ]),

                Section::make('Account Status')
                    ->schema([
                        Grid::make(3)->schema([
                            Select::make('status')
                                ->options([
                                    'active' => 'Active',
                                    'inactive' => 'Inactive',
                                    'blocked' => 'Blocked',
                                ])
                                ->default('active')
                                ->required(),
                            
                            TextInput::make('loyalty_points')
                                ->numeric()
                                ->default(0)
                                ->disabled()
                                ->dehydrated(false),
                            
                            TextInput::make('total_spent')
                                ->numeric()
                                ->prefix('₫')
                                ->disabled()
                                ->dehydrated(false),
                        ]),
                        
                        Textarea::make('notes')
                            ->maxLength(1000)
                            ->rows(3)
                            ->placeholder('Additional notes about the customer'),
                    ]),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                ImageColumn::make('avatar_url')
                    ->circular()
                    ->defaultImageUrl(fn ($record) => $record->avatar_url),
                
                TextColumn::make('code')
                    ->searchable()
                    ->sortable()
                    ->copyable()
                    ->weight(FontWeight::Bold),
                
                TextColumn::make('name')
                    ->searchable()
                    ->sortable()
                    ->limit(30),
                
                TextColumn::make('phone')
                    ->searchable()
                    ->copyable()
                    ->icon('heroicon-o-phone'),
                
                TextColumn::make('customer_group')
                    ->badge()
                    ->color(fn (string $state): string => match ($state) {
                        'vip' => 'success',
                        'wholesale' => 'warning',
                        'retail' => 'gray',
                        default => 'gray',
                    })
                    ->formatStateUsing(fn (string $state): string => ucfirst($state)),
                
                TextColumn::make('loyalty_points')
                    ->numeric()
                    ->badge()
                    ->color('info')
                    ->icon('heroicon-o-star'),
                
                TextColumn::make('total_spent')
                    ->money('VND')
                    ->sortable()
                    ->color('success'),
                
                TextColumn::make('last_visit')
                    ->dateTime()
                    ->sortable()
                    ->placeholder('Never')
                    ->since(),
                
                TextColumn::make('status')
                    ->badge()
                    ->color(fn (string $state): string => match ($state) {
                        'active' => 'success',
                        'inactive' => 'warning',
                        'blocked' => 'danger',
                        default => 'gray',
                    }),
            ])
            ->defaultSort('created_at', 'desc')
            ->filters([
                SelectFilter::make('customer_group')
                    ->options([
                        'retail' => 'Retail',
                        'wholesale' => 'Wholesale',
                        'vip' => 'VIP',
                    ])
                    ->multiple(),
                
                SelectFilter::make('status')
                    ->options([
                        'active' => 'Active',
                        'inactive' => 'Inactive',
                        'blocked' => 'Blocked',
                    ]),
                
                Filter::make('high_value')
                    ->query(fn (Builder $query): Builder => 
                        $query->where('total_spent', '>=', 1000000)
                    )
                    ->label('High Value (≥1M)')
                    ->toggle(),
                
                Filter::make('recent_customers')
                    ->query(fn (Builder $query): Builder => 
                        $query->where('created_at', '>=', now()->subDays(30))
                    )
                    ->label('New Customers (30 days)')
                    ->toggle(),
                
                Filter::make('inactive_customers')
                    ->query(fn (Builder $query): Builder => 
                        $query->where('last_visit', '<', now()->subDays(90))
                            ->orWhereNull('last_visit')
                    )
                    ->label('Inactive (90+ days)')
                    ->toggle(),
            ])
            ->actions([
                Tables\Actions\ActionGroup::make([
                    Tables\Actions\ViewAction::make(),
                    Tables\Actions\EditAction::make(),
                    Tables\Actions\Action::make('view_invoices')
                        ->icon('heroicon-o-document-text')
                        ->url(fn (Customer $record): string => 
                            InvoiceResource::getUrl('index', ['customer' => $record->id])
                        ),
                    Tables\Actions\Action::make('send_invoice_link')
                        ->icon('heroicon-o-link')
                        ->action(function (Customer $record) {
                            // Generate secure link for customer portal
                            $token = Str::random(32);
                            // Send SMS/Email with link
                        })
                        ->requiresConfirmation(),
                    Tables\Actions\Action::make('adjust_points')
                        ->icon('heroicon-o-star')
                        ->form([
                            TextInput::make('points')
                                ->numeric()
                                ->required(),
                            TextInput::make('reason')
                                ->required(),
                        ])
                        ->action(function (Customer $record, array $data) {
                            $record->loyaltyTransactions()->create([
                                'type' => $data['points'] > 0 ? 'earn' : 'redeem',
                                'points' => $data['points'],
                                'description' => $data['reason'],
                            ]);
                            $record->increment('loyalty_points', $data['points']);
                        }),
                ]),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                    Tables\Actions\BulkAction::make('activate')
                        ->action(fn (Collection $records) => 
                            $records->each->update(['status' => 'active'])
                        )
                        ->requiresConfirmation()
                        ->icon('heroicon-o-check-circle')
                        ->color('success'),
                    Tables\Actions\BulkAction::make('send_promotion')
                        ->form([
                            Textarea::make('message')
                                ->required()
                                ->placeholder('Promotion message'),
                        ])
                        ->action(function (Collection $records, array $data) {
                            // Send SMS/Email to selected customers
                        })
                        ->icon('heroicon-o-megaphone'),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            InvoicesRelationManager::class,
            LoyaltyTransactionsRelationManager::class,
        ];
    }

    public static function getWidgets(): array
    {
        return [
            CustomerStatsWidget::class,
        ];
    }
}
```

## Customer Portal

### 1. Customer Portal Controller
```php
// app/Http/Controllers/CustomerPortalController.php
class CustomerPortalController extends Controller
{
    public function viewInvoice(Request $request, string $uuid)
    {
        $invoice = Invoice::where('uuid', $uuid)
            ->with(['customer', 'items.batch.drug', 'user'])
            ->firstOrFail();
        
        // Generate QR code for payment
        $qrCode = QrCode::size(200)->generate(
            route('customer.invoice.pay', $invoice->uuid)
        );
        
        return view('customer-portal.invoice', compact('invoice', 'qrCode'));
    }

    public function downloadInvoicePdf(string $uuid)
    {
        $invoice = Invoice::where('uuid', $uuid)->firstOrFail();
        
        $pdf = PDF::loadView('invoices.pdf', compact('invoice'));
        
        return $pdf->download("invoice-{$invoice->invoice_number}.pdf");
    }

    public function paymentCallback(Request $request, string $uuid)
    {
        $invoice = Invoice::where('uuid', $uuid)->firstOrFail();
        
        // Process payment callback
        if ($request->get('status') === 'success') {
            $invoice->update(['payment_status' => 'paid']);
            
            // Award loyalty points
            if ($invoice->customer) {
                $invoice->customer->earnPoints($invoice->total_amount);
            }
        }
        
        return redirect()->route('customer.invoice', $uuid)
            ->with('success', 'Payment processed successfully');
    }
}
```

### 2. Customer Portal Views
```blade
{{-- resources/views/customer-portal/invoice.blade.php --}}
@extends('layouts.customer-portal')

@section('content')
<div class="max-w-4xl mx-auto p-6">
    <div class="bg-white shadow-lg rounded-lg overflow-hidden">
        <div class="bg-blue-600 text-white p-6">
            <h1 class="text-2xl font-bold">Invoice #{{ $invoice->invoice_number }}</h1>
            <p class="text-blue-100">{{ $invoice->created_at->format('d/m/Y H:i') }}</p>
        </div>
        
        <div class="p-6">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                <div>
                    <h3 class="font-semibold text-gray-700 mb-2">Customer Information</h3>
                    <p class="text-gray-600">{{ $invoice->customer?->name ?? 'Walk-in Customer' }}</p>
                    @if($invoice->customer?->phone)
                        <p class="text-gray-600">{{ $invoice->customer->phone }}</p>
                    @endif
                </div>
                
                <div>
                    <h3 class="font-semibold text-gray-700 mb-2">Payment Status</h3>
                    <span class="px-3 py-1 rounded-full text-sm font-medium
                        {{ $invoice->payment_status === 'paid' ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800' }}">
                        {{ ucfirst($invoice->payment_status) }}
                    </span>
                </div>
            </div>
            
            <div class="overflow-x-auto">
                <table class="w-full border-collapse border border-gray-300">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="border border-gray-300 px-4 py-2 text-left">Drug</th>
                            <th class="border border-gray-300 px-4 py-2 text-center">Qty</th>
                            <th class="border border-gray-300 px-4 py-2 text-right">Unit Price</th>
                            <th class="border border-gray-300 px-4 py-2 text-right">Total</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach($invoice->items as $item)
                        <tr>
                            <td class="border border-gray-300 px-4 py-2">
                                {{ $item->batch->drug->name }}
                                <br><small class="text-gray-500">Batch: {{ $item->batch->batch_number }}</small>
                            </td>
                            <td class="border border-gray-300 px-4 py-2 text-center">{{ $item->quantity }}</td>
                            <td class="border border-gray-300 px-4 py-2 text-right">₫{{ number_format($item->unit_price) }}</td>
                            <td class="border border-gray-300 px-4 py-2 text-right">₫{{ number_format($item->total_price) }}</td>
                        </tr>
                        @endforeach
                    </tbody>
                    <tfoot class="bg-gray-50">
                        <tr>
                            <td colspan="3" class="border border-gray-300 px-4 py-2 text-right font-semibold">Subtotal:</td>
                            <td class="border border-gray-300 px-4 py-2 text-right">₫{{ number_format($invoice->subtotal) }}</td>
                        </tr>
                        <tr>
                            <td colspan="3" class="border border-gray-300 px-4 py-2 text-right font-semibold">Tax:</td>
                            <td class="border border-gray-300 px-4 py-2 text-right">₫{{ number_format($invoice->tax_amount) }}</td>
                        </tr>
                        <tr>
                            <td colspan="3" class="border border-gray-300 px-4 py-2 text-right font-bold">Total:</td>
                            <td class="border border-gray-300 px-4 py-2 text-right font-bold">₫{{ number_format($invoice->total_amount) }}</td>
                        </tr>
                    </tfoot>
                </table>
            </div>
            
            @if($invoice->payment_status !== 'paid')
            <div class="mt-6 text-center">
                <div class="mb-4">
                    <h3 class="text-lg font-semibold mb-2">Scan to Pay</h3>
                    {!! $qrCode !!}
                </div>
                <p class="text-gray-600">Scan this QR code with your banking app to pay</p>
            </div>
            @endif
            
            <div class="mt-6 flex justify-center space-x-4">
                <a href="{{ route('customer.invoice.pdf', $invoice->uuid) }}" 
                   class="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700">
                    Download PDF
                </a>
                
                @if($invoice->customer?->loyalty_points > 0)
                <div class="bg-yellow-100 text-yellow-800 px-4 py-2 rounded">
                    Loyalty Points: {{ $invoice->customer->loyalty_points }}
                </div>
                @endif
            </div>
        </div>
    </div>
</div>
@endsection
```

## Câu lệnh để bắt đầu
"Hãy tạo Customer management system với Filament resources, loyalty program và customer portal để xem hóa đơn với QR code payment cho SaaS hiệu thuốc"
