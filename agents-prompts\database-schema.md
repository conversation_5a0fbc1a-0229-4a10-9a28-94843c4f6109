# 🗄️ Database Schema - <PERSON><PERSON> thống Hiệu thuốc

## Tổng quan
Schema database cho hệ thống quản lý bán hàng hiệu thuốc với đầy đủ các bảng và relationships.

## Core Tables

### 1. drugs (<PERSON>huố<PERSON>)
```sql
CREATE TABLE drugs (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    code VARCHAR(20) UNIQUE NOT NULL COMMENT 'Mã thuốc',
    name VARCHAR(255) NOT NULL COMMENT 'Tên thuốc',
    generic_name VARCHAR(255) COMMENT 'Tên hoạt chất',
    active_ingredient TEXT COMMENT 'Thành phần hoạt chất',
    concentration VARCHAR(100) COMMENT 'Nồng độ/hàm lượng',
    dosage_form VARCHAR(50) COMMENT 'Dạng bào chế',
    unit VARCHAR(20) NOT NULL COMMENT 'Đơn vị tính',
    manufacturer VARCHAR(255) COMMENT '<PERSON>hà sản xuất',
    country_of_origin VARCHAR(100) COMMENT 'Nước sản xuất',
    prescription_required BOOLEAN DEFAULT FALSE COMMENT 'Thuốc kê đơn',
    controlled_substance BOOLEAN DEFAULT FALSE COMMENT 'Chất gây nghiện',
    storage_condition TEXT COMMENT 'Điều kiện bảo quản',
    therapeutic_class VARCHAR(100) COMMENT 'Nhóm điều trị',
    price DECIMAL(15,2) NOT NULL COMMENT 'Giá bán lẻ',
    wholesale_price DECIMAL(15,2) COMMENT 'Giá bán sỉ',
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP NULL,
    
    INDEX idx_drugs_code (code),
    INDEX idx_drugs_name (name),
    INDEX idx_drugs_prescription (prescription_required),
    INDEX idx_drugs_active (is_active),
    FULLTEXT idx_drugs_search (name, generic_name, active_ingredient)
);
```

### 2. batches (Lô thuốc)
```sql
CREATE TABLE batches (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    drug_id BIGINT UNSIGNED NOT NULL,
    batch_number VARCHAR(50) NOT NULL COMMENT 'Số lô',
    manufacture_date DATE COMMENT 'Ngày sản xuất',
    expiry_date DATE NOT NULL COMMENT 'Hạn sử dụng',
    import_date DATE NOT NULL COMMENT 'Ngày nhập kho',
    import_price DECIMAL(15,2) NOT NULL COMMENT 'Giá nhập',
    selling_price DECIMAL(15,2) NOT NULL COMMENT 'Giá bán',
    initial_quantity INT NOT NULL COMMENT 'Số lượng nhập ban đầu',
    current_quantity INT NOT NULL COMMENT 'Số lượng hiện tại',
    reserved_quantity INT DEFAULT 0 COMMENT 'Số lượng đã đặt',
    supplier_id BIGINT UNSIGNED COMMENT 'Nhà cung cấp',
    import_invoice_number VARCHAR(50) COMMENT 'Số hóa đơn nhập',
    notes TEXT COMMENT 'Ghi chú',
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (drug_id) REFERENCES drugs(id) ON DELETE CASCADE,
    INDEX idx_batches_drug (drug_id),
    INDEX idx_batches_expiry (expiry_date),
    INDEX idx_batches_quantity (current_quantity),
    INDEX idx_batches_batch_number (batch_number),
    UNIQUE KEY uk_batches_drug_batch (drug_id, batch_number)
);
```

### 3. customers (Khách hàng)
```sql
CREATE TABLE customers (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    code VARCHAR(20) UNIQUE NOT NULL COMMENT 'Mã khách hàng',
    type ENUM('individual', 'company') DEFAULT 'individual' COMMENT 'Loại KH',
    name VARCHAR(255) NOT NULL COMMENT 'Tên khách hàng',
    phone VARCHAR(15) UNIQUE COMMENT 'Số điện thoại',
    email VARCHAR(255) COMMENT 'Email',
    date_of_birth DATE COMMENT 'Ngày sinh',
    gender ENUM('male', 'female', 'other') COMMENT 'Giới tính',
    id_number VARCHAR(20) COMMENT 'CMND/CCCD',
    address TEXT COMMENT 'Địa chỉ',
    ward VARCHAR(100) COMMENT 'Phường/xã',
    district VARCHAR(100) COMMENT 'Quận/huyện',
    province VARCHAR(100) COMMENT 'Tỉnh/thành phố',
    customer_group ENUM('retail', 'wholesale', 'vip') DEFAULT 'retail' COMMENT 'Nhóm KH',
    tax_code VARCHAR(15) COMMENT 'Mã số thuế',
    company_name VARCHAR(255) COMMENT 'Tên công ty',
    loyalty_points INT DEFAULT 0 COMMENT 'Điểm tích lũy',
    total_spent DECIMAL(15,2) DEFAULT 0 COMMENT 'Tổng tiền đã mua',
    last_visit TIMESTAMP NULL COMMENT 'Lần mua cuối',
    status ENUM('active', 'inactive', 'blocked') DEFAULT 'active',
    notes TEXT COMMENT 'Ghi chú',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP NULL,
    
    INDEX idx_customers_phone (phone),
    INDEX idx_customers_group (customer_group),
    INDEX idx_customers_status (status),
    FULLTEXT idx_customers_search (name, phone, email)
);
```

### 4. invoices (Hóa đơn)
```sql
CREATE TABLE invoices (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    invoice_number VARCHAR(50) UNIQUE NOT NULL COMMENT 'Số hóa đơn',
    customer_id BIGINT UNSIGNED COMMENT 'ID khách hàng',
    user_id BIGINT UNSIGNED NOT NULL COMMENT 'Nhân viên bán',
    invoice_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT 'Ngày xuất HĐ',
    subtotal DECIMAL(15,2) NOT NULL COMMENT 'Tổng tiền trước thuế',
    discount_amount DECIMAL(15,2) DEFAULT 0 COMMENT 'Tiền giảm giá',
    tax_amount DECIMAL(15,2) NOT NULL COMMENT 'Tiền thuế GTGT',
    total_amount DECIMAL(15,2) NOT NULL COMMENT 'Tổng tiền thanh toán',
    payment_method ENUM('cash', 'card', 'transfer', 'mixed') DEFAULT 'cash',
    payment_status ENUM('pending', 'paid', 'partial', 'refunded') DEFAULT 'paid',
    status ENUM('draft', 'confirmed', 'cancelled') DEFAULT 'confirmed',
    tax_authority_status ENUM('pending', 'sent', 'accepted', 'rejected') DEFAULT 'pending',
    tax_transaction_id VARCHAR(100) COMMENT 'Mã giao dịch cơ quan thuế',
    digital_signature TEXT COMMENT 'Chữ ký số',
    notes TEXT COMMENT 'Ghi chú',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (customer_id) REFERENCES customers(id) ON DELETE SET NULL,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE RESTRICT,
    INDEX idx_invoices_customer (customer_id),
    INDEX idx_invoices_user (user_id),
    INDEX idx_invoices_date (invoice_date),
    INDEX idx_invoices_status (status),
    INDEX idx_invoices_number (invoice_number)
);
```

### 5. invoice_items (Chi tiết hóa đơn)
```sql
CREATE TABLE invoice_items (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    invoice_id BIGINT UNSIGNED NOT NULL,
    batch_id BIGINT UNSIGNED NOT NULL,
    quantity INT NOT NULL COMMENT 'Số lượng',
    unit_price DECIMAL(15,2) NOT NULL COMMENT 'Đơn giá',
    discount_percentage DECIMAL(5,2) DEFAULT 0 COMMENT '% giảm giá',
    discount_amount DECIMAL(15,2) DEFAULT 0 COMMENT 'Tiền giảm giá',
    tax_rate DECIMAL(5,2) NOT NULL COMMENT 'Thuế suất GTGT',
    tax_amount DECIMAL(15,2) NOT NULL COMMENT 'Tiền thuế',
    total_price DECIMAL(15,2) NOT NULL COMMENT 'Thành tiền',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (invoice_id) REFERENCES invoices(id) ON DELETE CASCADE,
    FOREIGN KEY (batch_id) REFERENCES batches(id) ON DELETE RESTRICT,
    INDEX idx_invoice_items_invoice (invoice_id),
    INDEX idx_invoice_items_batch (batch_id)
);
```

## Authentication & Authorization Tables

### 6. users (Người dùng)
```sql
CREATE TABLE users (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(255) UNIQUE NOT NULL,
    email_verified_at TIMESTAMP NULL,
    password VARCHAR(255) NOT NULL,
    full_name VARCHAR(255) NOT NULL,
    phone VARCHAR(15),
    employee_id VARCHAR(20) UNIQUE,
    department VARCHAR(100),
    position VARCHAR(100),
    is_active BOOLEAN DEFAULT TRUE,
    last_login_at TIMESTAMP NULL,
    password_changed_at TIMESTAMP NULL,
    two_factor_enabled BOOLEAN DEFAULT FALSE,
    two_factor_secret VARCHAR(255),
    remember_token VARCHAR(100),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_users_username (username),
    INDEX idx_users_email (email),
    INDEX idx_users_active (is_active)
);
```

### 7. roles (Vai trò)
```sql
CREATE TABLE roles (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(50) UNIQUE NOT NULL,
    display_name VARCHAR(100) NOT NULL,
    description TEXT,
    is_system BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

### 8. permissions (Quyền hạn)
```sql
CREATE TABLE permissions (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) UNIQUE NOT NULL,
    display_name VARCHAR(150) NOT NULL,
    description TEXT,
    module VARCHAR(50),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_permissions_module (module)
);
```

## Supporting Tables

### 9. suppliers (Nhà cung cấp)
```sql
CREATE TABLE suppliers (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    code VARCHAR(20) UNIQUE NOT NULL,
    name VARCHAR(255) NOT NULL,
    contact_person VARCHAR(255),
    phone VARCHAR(15),
    email VARCHAR(255),
    address TEXT,
    tax_code VARCHAR(15),
    bank_account VARCHAR(50),
    bank_name VARCHAR(255),
    payment_terms VARCHAR(100),
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_suppliers_code (code),
    INDEX idx_suppliers_active (is_active)
);
```

### 10. audit_logs (Log audit)
```sql
CREATE TABLE audit_logs (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    user_id BIGINT UNSIGNED,
    action VARCHAR(50) NOT NULL,
    model_type VARCHAR(100) NOT NULL,
    model_id BIGINT UNSIGNED,
    old_values JSON,
    new_values JSON,
    ip_address VARCHAR(45),
    user_agent TEXT,
    performed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL,
    INDEX idx_audit_logs_user (user_id),
    INDEX idx_audit_logs_model (model_type, model_id),
    INDEX idx_audit_logs_action (action),
    INDEX idx_audit_logs_date (performed_at)
);
```

## Junction Tables

### 11. user_roles
```sql
CREATE TABLE user_roles (
    user_id BIGINT UNSIGNED NOT NULL,
    role_id BIGINT UNSIGNED NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    PRIMARY KEY (user_id, role_id),
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (role_id) REFERENCES roles(id) ON DELETE CASCADE
);
```

### 12. role_permissions
```sql
CREATE TABLE role_permissions (
    role_id BIGINT UNSIGNED NOT NULL,
    permission_id BIGINT UNSIGNED NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    PRIMARY KEY (role_id, permission_id),
    FOREIGN KEY (role_id) REFERENCES roles(id) ON DELETE CASCADE,
    FOREIGN KEY (permission_id) REFERENCES permissions(id) ON DELETE CASCADE
);
```

## Indexes & Performance

### Composite Indexes
```sql
-- Tìm kiếm thuốc theo tên và trạng thái
ALTER TABLE drugs ADD INDEX idx_drugs_name_active (name, is_active);

-- Tìm lô thuốc theo drug và hạn sử dụng
ALTER TABLE batches ADD INDEX idx_batches_drug_expiry (drug_id, expiry_date);

-- Tìm hóa đơn theo khách hàng và ngày
ALTER TABLE invoices ADD INDEX idx_invoices_customer_date (customer_id, invoice_date);

-- Tìm chi tiết hóa đơn theo hóa đơn và batch
ALTER TABLE invoice_items ADD INDEX idx_invoice_items_invoice_batch (invoice_id, batch_id);
```

### Full-text Search
```sql
-- Tìm kiếm thuốc
ALTER TABLE drugs ADD FULLTEXT idx_drugs_fulltext (name, generic_name, active_ingredient);

-- Tìm kiếm khách hàng
ALTER TABLE customers ADD FULLTEXT idx_customers_fulltext (name, phone, email);
```
