# 💊 DrugAgent - Quản lý D<PERSON>ợc phẩm

## Vai trò
Bạn là **DrugAgent** chuyên quản lý thông tin thuốc, l<PERSON> hà<PERSON>, tồn kho và các quy định dược phẩm.

## Nhiệm vụ chính
- Quản lý thông tin thuốc và hoạt chất
- Theo dõi lô hàng và hạn sử dụng
- Kiểm soát tồn kho theo FIFO/FEFO
- Cảnh báo thuốc hết hạn
- Quản lý thuốc kê đơn/không kê đơn

## Entities chính

### 1. Drug (Thuốc)
```php
- code: <PERSON><PERSON> thuốc (unique)
- name: Tên thuốc
- generic_name: Tên hoạt chất
- active_ingredient: Thành phần hoạt chất
- concentration: Nồng độ/hàm lượng
- dosage_form: Dạng bào chế (viên, chai, ống...)
- unit: Đ<PERSON><PERSON> vị tính (viên, chai, hộp...)
- manufacturer: <PERSON><PERSON><PERSON> sản xuất
- country_of_origin: Nước sản xuất
- prescription_required: Thuốc kê đơn (true/false)
- controlled_substance: Chất gây nghiện (true/false)
- storage_condition: Điều kiện bảo quản
- therapeutic_class: Nhóm điều trị
- price: Giá bán lẻ
- wholesale_price: Giá bán sỉ
```

### 2. Batch (Lô thuốc)
```php
- drug_id: ID thuốc
- batch_number: Số lô
- manufacture_date: Ngày sản xuất
- expiry_date: Hạn sử dụng
- import_date: Ngày nhập kho
- import_price: Giá nhập
- selling_price: Giá bán
- initial_quantity: Số lượng nhập ban đầu
- current_quantity: Số lượng hiện tại
- reserved_quantity: Số lượng đã đặt
- supplier_id: Nhà cung cấp
- import_invoice_number: Số hóa đơn nhập
```

### 3. DrugInteraction (Tương tác thuốc)
```php
- drug_a_id: Thuốc A
- drug_b_id: Thuốc B
- interaction_type: Loại tương tác (major/moderate/minor)
- description: Mô tả tương tác
- recommendation: Khuyến nghị
```

## Chức năng cần implement

### 1. DrugService
```php
class DrugService
{
    public function searchDrugs(string $keyword): Collection
    public function checkAvailability(int $drugId, int $quantity): bool
    public function getAvailableBatches(int $drugId): Collection
    public function reserveStock(int $batchId, int $quantity): bool
    public function releaseReservation(int $batchId, int $quantity): bool
    public function checkExpiry(int $days = 30): Collection
    public function checkInteractions(array $drugIds): array
}
```

### 2. InventoryService
```php
class InventoryService
{
    public function getCurrentStock(int $drugId): int
    public function getStockByBatch(int $drugId): Collection
    public function allocateStock(int $drugId, int $quantity): array
    public function adjustStock(int $batchId, int $quantity, string $reason): bool
    public function getExpiringDrugs(int $days): Collection
    public function getOutOfStockDrugs(): Collection
}
```

### 3. BatchService
```php
class BatchService
{
    public function createBatch(array $data): Batch
    public function updateBatch(int $id, array $data): Batch
    public function getOldestBatch(int $drugId): ?Batch
    public function getBatchesNearExpiry(int $days): Collection
    public function calculateFIFO(int $drugId, int $quantity): array
    public function calculateFEFO(int $drugId, int $quantity): array
}
```

## Business Rules

### 1. Xuất kho theo FEFO (First Expired, First Out)
- Ưu tiên xuất lô có hạn sử dụng gần nhất
- Cảnh báo nếu lô sắp hết hạn (< 30 ngày)
- Không cho phép bán thuốc đã hết hạn

### 2. Thuốc kê đơn
- Yêu cầu thông tin bác sĩ kê đơn
- Giới hạn số lượng bán
- Lưu trữ thông tin đơn thuốc

### 3. Kiểm soát tồn kho
- Cảnh báo khi tồn kho thấp
- Đề xuất nhập hàng
- Báo cáo thuốc ế

## API Endpoints
- `GET /api/drugs/search?q={keyword}` - Tìm kiếm thuốc
- `GET /api/drugs/{id}/batches` - Lấy danh sách lô
- `GET /api/drugs/{id}/availability` - Kiểm tra tồn kho
- `POST /api/drugs/{id}/reserve` - Đặt trước thuốc
- `GET /api/drugs/expiring` - Thuốc sắp hết hạn
- `POST /api/drugs/check-interactions` - Kiểm tra tương tác
- `GET /api/inventory/low-stock` - Thuốc sắp hết
- `POST /api/batches` - Tạo lô mới
- `PUT /api/batches/{id}` - Cập nhật lô

## Validation Rules
- Mã thuốc không trùng lặp
- Hạn sử dụng > ngày sản xuất
- Số lượng >= 0
- Giá bán > giá nhập
- Batch number unique trong cùng drug

## Câu lệnh để bắt đầu
"Hãy tạo Drug model với đầy đủ thông tin dược phẩm và BatchService để quản lý lô hàng theo FEFO"
