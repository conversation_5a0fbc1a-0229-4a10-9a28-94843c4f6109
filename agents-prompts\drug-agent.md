# 💊 DrugAgent - Inventory Management

## Vai trò
Bạn là **DrugAgent** chuyên quản lý thuốc, lô hàng và tồn kho cho hệ thống SaaS hiệu thuốc với Filament 3.

## <PERSON><PERSON><PERSON><PERSON> vụ chính
- Quản lý thông tin thuốc với Filament forms
- <PERSON> dõi lô hàng và FEFO (First Expired, First Out)
- <PERSON>ảnh báo thuốc hết hạn và tồn kho thấp
- Tương tác thuốc và contraindications
- Import/Export inventory data

## Drug Management Models

### 1. Drug Model
```php
// app/Models/Drug.php
class Drug extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'code', 'name', 'generic_name', 'active_ingredient',
        'concentration', 'dosage_form', 'unit', 'manufacturer',
        'country_of_origin', 'prescription_required', 'controlled_substance',
        'storage_condition', 'therapeutic_class', 'price', 'wholesale_price',
        'is_active', 'description', 'side_effects', 'contraindications'
    ];

    protected $casts = [
        'prescription_required' => 'boolean',
        'controlled_substance' => 'boolean',
        'is_active' => 'boolean',
        'price' => 'decimal:2',
        'wholesale_price' => 'decimal:2',
        'side_effects' => 'array',
        'contraindications' => 'array',
    ];

    // Relationships
    public function batches()
    {
        return $this->hasMany(Batch::class);
    }

    public function invoiceItems()
    {
        return $this->hasManyThrough(InvoiceItem::class, Batch::class);
    }

    public function interactions()
    {
        return $this->belongsToMany(Drug::class, 'drug_interactions', 'drug_a_id', 'drug_b_id')
            ->withPivot('interaction_type', 'severity', 'description');
    }

    // Scopes
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    public function scopePrescriptionRequired($query)
    {
        return $query->where('prescription_required', true);
    }

    public function scopeLowStock($query, $threshold = 10)
    {
        return $query->whereHas('batches', function ($q) use ($threshold) {
            $q->selectRaw('drug_id, SUM(current_quantity) as total_stock')
                ->groupBy('drug_id')
                ->havingRaw('SUM(current_quantity) < ?', [$threshold]);
        });
    }

    // Accessors & Mutators
    public function getCurrentStockAttribute(): int
    {
        return $this->batches->sum('current_quantity');
    }

    public function getAvailableStockAttribute(): int
    {
        return $this->batches
            ->where('expiry_date', '>', now())
            ->sum('current_quantity');
    }

    public function getTaxRateAttribute(): float
    {
        return $this->prescription_required ? 0.05 : 0.10; // 5% or 10% VAT
    }

    // Business Methods
    public function canSell(int $quantity): bool
    {
        return $this->available_stock >= $quantity;
    }

    public function getOldestBatches(int $quantity = null)
    {
        $query = $this->batches()
            ->where('current_quantity', '>', 0)
            ->where('expiry_date', '>', now())
            ->orderBy('expiry_date', 'asc');

        return $quantity ? $query->take($quantity)->get() : $query->get();
    }

    public function allocateStock(int $quantity): array
    {
        $allocation = [];
        $remaining = $quantity;
        
        foreach ($this->getOldestBatches() as $batch) {
            if ($remaining <= 0) break;
            
            $allocatedQty = min($remaining, $batch->current_quantity);
            $allocation[] = [
                'batch_id' => $batch->id,
                'quantity' => $allocatedQty,
                'price' => $batch->selling_price,
            ];
            
            $remaining -= $allocatedQty;
        }
        
        if ($remaining > 0) {
            throw new InsufficientStockException("Not enough stock. Missing: {$remaining} units");
        }
        
        return $allocation;
    }
}
```

### 2. Batch Model
```php
// app/Models/Batch.php
class Batch extends Model
{
    use HasFactory;

    protected $fillable = [
        'drug_id', 'batch_number', 'manufacture_date', 'expiry_date',
        'import_date', 'import_price', 'selling_price', 'initial_quantity',
        'current_quantity', 'reserved_quantity', 'supplier_id',
        'import_invoice_number', 'notes'
    ];

    protected $casts = [
        'manufacture_date' => 'date',
        'expiry_date' => 'date',
        'import_date' => 'date',
        'import_price' => 'decimal:2',
        'selling_price' => 'decimal:2',
        'initial_quantity' => 'integer',
        'current_quantity' => 'integer',
        'reserved_quantity' => 'integer',
    ];

    // Relationships
    public function drug()
    {
        return $this->belongsTo(Drug::class);
    }

    public function supplier()
    {
        return $this->belongsTo(Supplier::class);
    }

    public function invoiceItems()
    {
        return $this->hasMany(InvoiceItem::class);
    }

    // Scopes
    public function scopeExpiringSoon($query, $days = 30)
    {
        return $query->where('expiry_date', '<=', now()->addDays($days))
            ->where('current_quantity', '>', 0);
    }

    public function scopeExpired($query)
    {
        return $query->where('expiry_date', '<', now());
    }

    public function scopeAvailable($query)
    {
        return $query->where('current_quantity', '>', 0)
            ->where('expiry_date', '>', now());
    }

    // Accessors
    public function getIsExpiredAttribute(): bool
    {
        return $this->expiry_date->isPast();
    }

    public function getIsExpiringSoonAttribute(): bool
    {
        return $this->expiry_date->diffInDays() <= 30;
    }

    public function getDaysToExpiryAttribute(): int
    {
        return max(0, $this->expiry_date->diffInDays());
    }

    public function getAvailableQuantityAttribute(): int
    {
        return $this->current_quantity - $this->reserved_quantity;
    }

    // Business Methods
    public function reserve(int $quantity): bool
    {
        if ($this->available_quantity < $quantity) {
            return false;
        }

        $this->increment('reserved_quantity', $quantity);
        return true;
    }

    public function releaseReservation(int $quantity): bool
    {
        if ($this->reserved_quantity < $quantity) {
            return false;
        }

        $this->decrement('reserved_quantity', $quantity);
        return true;
    }

    public function sell(int $quantity): bool
    {
        if ($this->current_quantity < $quantity) {
            return false;
        }

        $this->decrement('current_quantity', $quantity);
        
        // Also reduce reservation if any
        if ($this->reserved_quantity > 0) {
            $reservationReduction = min($this->reserved_quantity, $quantity);
            $this->decrement('reserved_quantity', $reservationReduction);
        }

        return true;
    }
}
```

## Filament Resources

### 1. Enhanced Drug Resource
```php
// app/Filament/Resources/DrugResource.php (Extended)
class DrugResource extends Resource
{
    protected static ?string $model = Drug::class;
    protected static ?string $navigationIcon = 'heroicon-o-beaker';
    protected static ?string $navigationGroup = 'Inventory';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Wizard::make([
                    Wizard\Step::make('Basic Information')
                        ->schema([
                            Grid::make(2)->schema([
                                TextInput::make('code')
                                    ->required()
                                    ->unique(ignoreRecord: true)
                                    ->maxLength(20)
                                    ->placeholder('DRG001')
                                    ->helperText('Unique drug code'),
                                
                                TextInput::make('name')
                                    ->required()
                                    ->maxLength(255)
                                    ->placeholder('Paracetamol 500mg'),
                            ]),
                            
                            Grid::make(2)->schema([
                                TextInput::make('generic_name')
                                    ->maxLength(255)
                                    ->placeholder('Paracetamol'),
                                
                                TextInput::make('concentration')
                                    ->maxLength(100)
                                    ->placeholder('500mg'),
                            ]),
                            
                            Textarea::make('active_ingredient')
                                ->maxLength(1000)
                                ->rows(3)
                                ->placeholder('Active pharmaceutical ingredients'),
                        ]),

                    Wizard\Step::make('Product Details')
                        ->schema([
                            Grid::make(3)->schema([
                                Select::make('dosage_form')
                                    ->options([
                                        'tablet' => 'Tablet',
                                        'capsule' => 'Capsule',
                                        'syrup' => 'Syrup',
                                        'injection' => 'Injection',
                                        'cream' => 'Cream',
                                        'drops' => 'Drops',
                                        'inhaler' => 'Inhaler',
                                        'patch' => 'Patch',
                                    ])
                                    ->searchable()
                                    ->required(),
                                
                                TextInput::make('unit')
                                    ->required()
                                    ->placeholder('Box, Bottle, Tube'),
                                
                                TextInput::make('manufacturer')
                                    ->maxLength(255),
                            ]),
                            
                            Grid::make(2)->schema([
                                TextInput::make('country_of_origin')
                                    ->maxLength(100),
                                
                                TextInput::make('therapeutic_class')
                                    ->maxLength(100)
                                    ->placeholder('Analgesic, Antibiotic, etc.'),
                            ]),
                            
                            Textarea::make('storage_condition')
                                ->maxLength(500)
                                ->placeholder('Store in cool, dry place'),
                        ]),

                    Wizard\Step::make('Regulations & Pricing')
                        ->schema([
                            Grid::make(2)->schema([
                                Toggle::make('prescription_required')
                                    ->label('Prescription Required')
                                    ->helperText('Check if this drug requires prescription'),
                                
                                Toggle::make('controlled_substance')
                                    ->label('Controlled Substance')
                                    ->helperText('Check if this is a controlled substance'),
                            ]),
                            
                            Grid::make(2)->schema([
                                TextInput::make('price')
                                    ->required()
                                    ->numeric()
                                    ->prefix('₫')
                                    ->step(1000)
                                    ->label('Retail Price'),
                                
                                TextInput::make('wholesale_price')
                                    ->numeric()
                                    ->prefix('₫')
                                    ->step(1000)
                                    ->label('Wholesale Price'),
                            ]),
                            
                            Toggle::make('is_active')
                                ->default(true)
                                ->label('Active Status'),
                        ]),

                    Wizard\Step::make('Medical Information')
                        ->schema([
                            Textarea::make('description')
                                ->maxLength(1000)
                                ->rows(3)
                                ->placeholder('Drug description and usage'),
                            
                            TagsInput::make('side_effects')
                                ->placeholder('Add side effects')
                                ->helperText('Press Enter to add each side effect'),
                            
                            TagsInput::make('contraindications')
                                ->placeholder('Add contraindications')
                                ->helperText('Press Enter to add each contraindication'),
                        ]),
                ])
                ->columnSpanFull()
                ->persistStepInQueryString()
                ->submitAction(new HtmlString(Blade::render(<<<BLADE
                    <x-filament::button
                        type="submit"
                        size="sm"
                    >
                        Create Drug
                    </x-filament::button>
                BLADE))),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                ImageColumn::make('image')
                    ->defaultImageUrl('/images/drug-placeholder.png')
                    ->circular(),
                
                TextColumn::make('code')
                    ->searchable()
                    ->sortable()
                    ->copyable()
                    ->weight(FontWeight::Bold),
                
                TextColumn::make('name')
                    ->searchable()
                    ->sortable()
                    ->limit(40)
                    ->tooltip(function (TextColumn $column): ?string {
                        $state = $column->getState();
                        return strlen($state) > 40 ? $state : null;
                    }),
                
                TextColumn::make('dosage_form')
                    ->badge()
                    ->color(fn (string $state): string => match ($state) {
                        'tablet' => 'success',
                        'injection' => 'danger',
                        'syrup' => 'warning',
                        'capsule' => 'info',
                        default => 'gray',
                    }),
                
                IconColumn::make('prescription_required')
                    ->boolean()
                    ->label('Rx')
                    ->tooltip('Prescription Required'),
                
                TextColumn::make('price')
                    ->money('VND')
                    ->sortable(),
                
                TextColumn::make('current_stock')
                    ->getStateUsing(fn (Drug $record): int => $record->current_stock)
                    ->badge()
                    ->color(fn (int $state): string => match (true) {
                        $state === 0 => 'danger',
                        $state < 10 => 'warning',
                        $state < 50 => 'info',
                        default => 'success',
                    })
                    ->icon(fn (int $state): string => match (true) {
                        $state === 0 => 'heroicon-o-x-circle',
                        $state < 10 => 'heroicon-o-exclamation-triangle',
                        default => 'heroicon-o-check-circle',
                    }),
                
                TextColumn::make('expiring_batches_count')
                    ->getStateUsing(fn (Drug $record): int => 
                        $record->batches()->expiringSoon()->count()
                    )
                    ->badge()
                    ->color('warning')
                    ->icon('heroicon-o-clock')
                    ->label('Expiring')
                    ->tooltip('Batches expiring in 30 days'),
                
                IconColumn::make('is_active')
                    ->boolean()
                    ->label('Active'),
            ])
            ->defaultSort('created_at', 'desc')
            ->filters([
                SelectFilter::make('dosage_form')
                    ->options([
                        'tablet' => 'Tablet',
                        'capsule' => 'Capsule',
                        'syrup' => 'Syrup',
                        'injection' => 'Injection',
                    ])
                    ->multiple(),
                
                TernaryFilter::make('prescription_required')
                    ->label('Prescription Required'),
                
                TernaryFilter::make('is_active')
                    ->label('Active Status'),
                
                Filter::make('low_stock')
                    ->query(fn (Builder $query): Builder => $query->lowStock())
                    ->label('Low Stock (< 10)')
                    ->toggle(),
                
                Filter::make('expiring_soon')
                    ->query(fn (Builder $query): Builder => 
                        $query->whereHas('batches', fn ($q) => $q->expiringSoon())
                    )
                    ->label('Has Expiring Batches')
                    ->toggle(),
                
                Filter::make('price_range')
                    ->form([
                        TextInput::make('price_from')
                            ->numeric()
                            ->prefix('₫'),
                        TextInput::make('price_to')
                            ->numeric()
                            ->prefix('₫'),
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        return $query
                            ->when(
                                $data['price_from'],
                                fn (Builder $query, $price): Builder => $query->where('price', '>=', $price),
                            )
                            ->when(
                                $data['price_to'],
                                fn (Builder $query, $price): Builder => $query->where('price', '<=', $price),
                            );
                    }),
            ])
            ->actions([
                Tables\Actions\ActionGroup::make([
                    Tables\Actions\ViewAction::make(),
                    Tables\Actions\EditAction::make(),
                    Tables\Actions\Action::make('manage_batches')
                        ->icon('heroicon-o-archive-box')
                        ->url(fn (Drug $record): string => 
                            BatchResource::getUrl('index', ['drug' => $record->id])
                        ),
                    Tables\Actions\Action::make('check_interactions')
                        ->icon('heroicon-o-exclamation-triangle')
                        ->action(function (Drug $record) {
                            // Implement interaction checking logic
                        }),
                ]),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                    Tables\Actions\BulkAction::make('activate')
                        ->action(fn (Collection $records) => 
                            $records->each->update(['is_active' => true])
                        )
                        ->requiresConfirmation()
                        ->icon('heroicon-o-check-circle')
                        ->color('success'),
                    Tables\Actions\BulkAction::make('deactivate')
                        ->action(fn (Collection $records) => 
                            $records->each->update(['is_active' => false])
                        )
                        ->requiresConfirmation()
                        ->icon('heroicon-o-x-circle')
                        ->color('danger'),
                ]),
            ])
            ->headerActions([
                Tables\Actions\Action::make('import')
                    ->icon('heroicon-o-arrow-up-tray')
                    ->action(function (array $data) {
                        // Implement CSV import
                    })
                    ->form([
                        FileUpload::make('file')
                            ->acceptedFileTypes(['text/csv', 'application/vnd.ms-excel'])
                            ->required(),
                    ]),
                Tables\Actions\Action::make('export')
                    ->icon('heroicon-o-arrow-down-tray')
                    ->action(function () {
                        // Implement CSV export
                        return response()->download(storage_path('app/exports/drugs.csv'));
                    }),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            BatchesRelationManager::class,
            InteractionsRelationManager::class,
        ];
    }

    public static function getWidgets(): array
    {
        return [
            DrugStatsWidget::class,
        ];
    }
}
```

## Inventory Services

### 1. Inventory Service
```php
// app/Services/InventoryService.php
class InventoryService
{
    public function getCurrentStock(Drug $drug): int
    {
        return $drug->batches()
            ->where('expiry_date', '>', now())
            ->sum('current_quantity');
    }

    public function getStockByBatch(Drug $drug): Collection
    {
        return $drug->batches()
            ->where('current_quantity', '>', 0)
            ->orderBy('expiry_date', 'asc')
            ->get();
    }

    public function allocateStock(Drug $drug, int $quantity): array
    {
        return $drug->allocateStock($quantity);
    }

    public function adjustStock(Batch $batch, int $quantity, string $reason): bool
    {
        $oldQuantity = $batch->current_quantity;
        $batch->update(['current_quantity' => $quantity]);

        // Log the adjustment
        StockAdjustment::create([
            'batch_id' => $batch->id,
            'old_quantity' => $oldQuantity,
            'new_quantity' => $quantity,
            'adjustment' => $quantity - $oldQuantity,
            'reason' => $reason,
            'user_id' => auth()->id(),
        ]);

        return true;
    }

    public function getExpiringDrugs(int $days = 30): Collection
    {
        return Batch::expiringSoon($days)
            ->with('drug')
            ->get()
            ->groupBy('drug_id')
            ->map(function ($batches) {
                return [
                    'drug' => $batches->first()->drug,
                    'total_quantity' => $batches->sum('current_quantity'),
                    'earliest_expiry' => $batches->min('expiry_date'),
                    'batches_count' => $batches->count(),
                ];
            });
    }

    public function getOutOfStockDrugs(): Collection
    {
        return Drug::whereDoesntHave('batches', function ($query) {
            $query->where('current_quantity', '>', 0)
                ->where('expiry_date', '>', now());
        })->get();
    }

    public function getLowStockDrugs(int $threshold = 10): Collection
    {
        return Drug::lowStock($threshold)->get();
    }
}
```

## Widgets

### 1. Inventory Dashboard Widget
```php
// app/Filament/Widgets/InventoryStatsWidget.php
class InventoryStatsWidget extends StatsOverviewWidget
{
    protected function getStats(): array
    {
        $inventoryService = app(InventoryService::class);
        
        return [
            Stat::make('Total Drugs', Drug::active()->count())
                ->description('Active drugs in inventory')
                ->descriptionIcon('heroicon-m-beaker')
                ->color('success'),
            
            Stat::make('Low Stock Items', $inventoryService->getLowStockDrugs()->count())
                ->description('Items with < 10 units')
                ->descriptionIcon('heroicon-m-exclamation-triangle')
                ->color('warning'),
            
            Stat::make('Expiring Soon', $inventoryService->getExpiringDrugs()->count())
                ->description('Expiring in 30 days')
                ->descriptionIcon('heroicon-m-clock')
                ->color('danger'),
            
            Stat::make('Out of Stock', $inventoryService->getOutOfStockDrugs()->count())
                ->description('No available stock')
                ->descriptionIcon('heroicon-m-x-circle')
                ->color('danger'),
        ];
    }
}
```

## Câu lệnh để bắt đầu
"Hãy tạo Drug và Batch models với Filament resources, bao gồm FEFO logic, inventory tracking và expiry management cho SaaS hiệu thuốc"
