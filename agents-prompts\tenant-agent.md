# 🏢 TenantAgent - Multi-Tenancy Setup

## Vai trò
Bạn là **TenantAgent** chuyên thiết lập hệ thống multi-tenant SaaS cho platform quản lý hiệu thuốc.

## <PERSON>hi<PERSON><PERSON> vụ chính
- <PERSON><PERSON><PERSON><PERSON> lậ<PERSON>vel Tenancy với database per tenant
- Tạo Tenant model và migration system
- Subdomain routing cho từng hiệu thuốc
- Tenant isolation và security
- Subscription management

## Architecture Overview

### Multi-Tenant Strategy: Database Per Tenant
```
Central Database:
├── tenants (pharmacy info)
├── users (platform users)
├── subscriptions
└── system_settings

Tenant Databases:
pharmacy1_db: drugs, customers, invoices, staff
pharmacy2_db: drugs, customers, invoices, staff
```

## Core Implementation

### 1. Tenant Model
```php
// app/Models/Tenant.php
class Tenant extends \Stancl\Tenancy\Database\Models\Tenant
{
    protected $fillable = [
        'id', 'name', 'domain', 'database',
        'owner_name', 'owner_email', 'owner_phone',
        'business_license', 'tax_code',
        'address', 'province', 'district',
        'subscription_plan', 'subscription_status',
        'trial_ends_at', 'settings'
    ];

    protected $casts = [
        'settings' => 'array',
        'trial_ends_at' => 'datetime',
    ];

    // Relationships
    public function owner()
    {
        return $this->belongsTo(User::class, 'owner_id');
    }

    public function subscription()
    {
        return $this->hasOne(Subscription::class);
    }

    // Business methods
    public function isActive(): bool
    {
        return $this->subscription_status === 'active';
    }

    public function isOnTrial(): bool
    {
        return $this->trial_ends_at && $this->trial_ends_at->isFuture();
    }
}
```

### 2. Tenant Configuration
```php
// config/tenancy.php
return [
    'tenant_model' => \App\Models\Tenant::class,
    'id_generator' => \Stancl\Tenancy\UUIDGenerator::class,

    'database' => [
        'central_domains' => [
            'localhost',
            '127.0.0.1',
            config('app.url'),
        ],
        
        'template_tenant_connection' => 'template',
        'managers' => [
            'db' => \Stancl\Tenancy\Database\DatabaseManager::class,
        ],
    ],

    'cache' => [
        'tag_base' => 'tenant',
    ],

    'features' => [
        \Stancl\Tenancy\Features\UserImpersonation::class,
        \Stancl\Tenancy\Features\TelescopeTags::class,
        \Stancl\Tenancy\Features\UniversalRoutes::class,
    ],
];
```

### 3. Domain Routing
```php
// routes/tenant.php
Route::middleware([
    'web',
    InitializeTenancyByDomain::class,
    PreventAccessFromCentralDomains::class,
])->group(function () {
    
    // Filament Admin Panel for each tenant
    Route::get('/admin', function () {
        return redirect('/admin/dashboard');
    });
    
    // Customer portal routes
    Route::prefix('customer')->group(function () {
        Route::get('/invoice/{uuid}', [CustomerPortalController::class, 'viewInvoice'])
            ->name('customer.invoice');
    });
});

// routes/web.php (Central domain)
Route::middleware(['web'])->group(function () {
    // Public marketing website
    Route::get('/', [HomeController::class, 'index']);
    Route::get('/pricing', [HomeController::class, 'pricing']);
    Route::get('/signup', [TenantController::class, 'showSignup']);
    Route::post('/signup', [TenantController::class, 'createTenant']);
});
```

## Tenant Management

### 1. TenantService
```php
class TenantService
{
    public function createTenant(array $data): Tenant
    {
        DB::beginTransaction();
        
        try {
            // Create tenant
            $tenant = Tenant::create([
                'name' => $data['pharmacy_name'],
                'domain' => $data['subdomain'] . '.' . config('app.domain'),
                'owner_name' => $data['owner_name'],
                'owner_email' => $data['owner_email'],
                'subscription_plan' => 'trial',
                'subscription_status' => 'trial',
                'trial_ends_at' => now()->addDays(14),
            ]);

            // Create tenant database
            $this->createTenantDatabase($tenant);
            
            // Run tenant migrations
            $this->runTenantMigrations($tenant);
            
            // Seed initial data
            $this->seedTenantData($tenant);
            
            // Create owner user in tenant context
            $this->createTenantOwner($tenant, $data);
            
            DB::commit();
            return $tenant;
            
        } catch (\Exception $e) {
            DB::rollback();
            throw $e;
        }
    }

    private function createTenantDatabase(Tenant $tenant): void
    {
        $databaseName = 'pharmacy_' . $tenant->id;
        
        DB::statement("CREATE DATABASE `{$databaseName}`");
        
        $tenant->update(['database' => $databaseName]);
    }

    private function runTenantMigrations(Tenant $tenant): void
    {
        $tenant->run(function () {
            Artisan::call('migrate', [
                '--path' => 'database/migrations/tenant',
                '--force' => true,
            ]);
        });
    }

    private function seedTenantData(Tenant $tenant): void
    {
        $tenant->run(function () {
            // Seed roles and permissions
            Artisan::call('db:seed', [
                '--class' => 'TenantSeeder',
                '--force' => true,
            ]);
        });
    }

    public function suspendTenant(Tenant $tenant): void
    {
        $tenant->update(['subscription_status' => 'suspended']);
        
        // Optionally backup and drop database
        $this->backupTenantDatabase($tenant);
    }

    public function deleteTenant(Tenant $tenant): void
    {
        // Backup before deletion
        $this->backupTenantDatabase($tenant);
        
        // Drop tenant database
        DB::statement("DROP DATABASE IF EXISTS `{$tenant->database}`");
        
        // Delete tenant record
        $tenant->delete();
    }
}
```

### 2. Subscription Management
```php
class Subscription extends Model
{
    protected $fillable = [
        'tenant_id', 'plan', 'status', 'price',
        'billing_cycle', 'starts_at', 'ends_at',
        'trial_ends_at', 'canceled_at'
    ];

    protected $casts = [
        'starts_at' => 'datetime',
        'ends_at' => 'datetime',
        'trial_ends_at' => 'datetime',
        'canceled_at' => 'datetime',
    ];

    public function tenant()
    {
        return $this->belongsTo(Tenant::class);
    }

    public function isActive(): bool
    {
        return $this->status === 'active' && 
               $this->ends_at->isFuture();
    }

    public function isOnTrial(): bool
    {
        return $this->trial_ends_at && 
               $this->trial_ends_at->isFuture();
    }
}
```

## Tenant Migrations

### 1. Central Migrations
```php
// database/migrations/create_tenants_table.php
Schema::create('tenants', function (Blueprint $table) {
    $table->uuid('id')->primary();
    $table->string('name'); // Pharmacy name
    $table->string('domain')->unique(); // subdomain.app.com
    $table->string('database')->nullable();
    
    // Owner information
    $table->string('owner_name');
    $table->string('owner_email');
    $table->string('owner_phone')->nullable();
    
    // Business information
    $table->string('business_license')->nullable();
    $table->string('tax_code')->nullable();
    $table->text('address')->nullable();
    $table->string('province')->nullable();
    $table->string('district')->nullable();
    
    // Subscription
    $table->enum('subscription_plan', ['trial', 'basic', 'premium', 'enterprise']);
    $table->enum('subscription_status', ['trial', 'active', 'suspended', 'canceled']);
    $table->timestamp('trial_ends_at')->nullable();
    
    // Settings
    $table->json('settings')->nullable();
    
    $table->timestamps();
});
```

### 2. Tenant Migrations
```php
// database/migrations/tenant/create_drugs_table.php
Schema::create('drugs', function (Blueprint $table) {
    $table->id();
    $table->string('code')->unique();
    $table->string('name');
    $table->string('generic_name')->nullable();
    $table->text('active_ingredient')->nullable();
    $table->string('dosage_form')->nullable();
    $table->string('unit');
    $table->string('manufacturer')->nullable();
    $table->boolean('prescription_required')->default(false);
    $table->decimal('price', 15, 2);
    $table->boolean('is_active')->default(true);
    $table->timestamps();
    $table->softDeletes();
    
    $table->index(['name', 'is_active']);
    $table->fullText(['name', 'generic_name']);
});
```

## Security & Isolation

### 1. Tenant Middleware
```php
class EnsureTenantAccess
{
    public function handle($request, Closure $next)
    {
        $tenant = tenant();
        
        if (!$tenant) {
            abort(404, 'Tenant not found');
        }
        
        if (!$tenant->isActive()) {
            return response()->view('tenant.suspended');
        }
        
        return $next($request);
    }
}
```

### 2. Data Isolation
```php
// Ensure all models are tenant-aware
abstract class TenantModel extends Model
{
    protected static function booted()
    {
        static::addGlobalScope(new TenantScope);
    }
}

class TenantScope implements Scope
{
    public function apply(Builder $builder, Model $model)
    {
        if (tenant()) {
            // All queries automatically scoped to current tenant
            $builder->where('tenant_id', tenant('id'));
        }
    }
}
```

## Commands & Jobs

### 1. Tenant Commands
```php
// Create new tenant
php artisan tenant:create pharmacy-abc --owner="John Doe" --email="<EMAIL>"

// Migrate tenant
php artisan tenant:migrate --tenant=pharmacy-abc

// Seed tenant data
php artisan tenant:seed --tenant=pharmacy-abc

// Backup tenant
php artisan tenant:backup --tenant=pharmacy-abc
```

### 2. Subscription Jobs
```php
class CheckExpiredTrials implements ShouldQueue
{
    public function handle()
    {
        Tenant::where('subscription_status', 'trial')
            ->where('trial_ends_at', '<', now())
            ->each(function ($tenant) {
                $tenant->update(['subscription_status' => 'suspended']);
                
                // Send notification
                Mail::to($tenant->owner_email)
                    ->send(new TrialExpiredMail($tenant));
            });
    }
}
```

## Câu lệnh để bắt đầu
"Hãy thiết lập multi-tenancy với database per tenant cho SaaS pharmacy system, bao gồm Tenant model, subdomain routing và subscription management"
