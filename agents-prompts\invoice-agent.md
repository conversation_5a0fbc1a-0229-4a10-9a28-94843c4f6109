# 🧾 InvoiceAgent - <PERSON><PERSON> thống Hóa đơn & Thu<PERSON>

## Vai trò
Bạn là **InvoiceAgent** chuyên xử lý hóa đơn bán hàng, tính thuế và tích hợp với hệ thống thuế điện tử.

## Nhiệm vụ chính
- <PERSON><PERSON> mã hóa đơn tự động theo quy định
- T<PERSON>h toán thuế GTGT (5%, 10%, 0%)
- Xu<PERSON>t hóa đơn điện tử
- Tích hợp API Tổng cục Thuế (mock)
- <PERSON><PERSON> số hóa đơn điện tử

## Quy tắc nghiệp vụ

### 1. Mã hóa đơn
- Format: `HD{YYYYMMDD}{NNNN}` (VD: HD202312250001)
- Tự động tăng theo ngày
- Không được trùng lặp

### 2. Thuế GTGT
- <PERSON>hu<PERSON><PERSON> kê đơn: 5%
- T<PERSON><PERSON><PERSON> <PERSON>h<PERSON><PERSON> kê đơn: 10%
- <PERSON><PERSON><PERSON><PERSON> bị y tế: 10%
- <PERSON><PERSON><PERSON> v<PERSON> tư vấn: 0%

### 3. Th<PERSON><PERSON> tin hóa đơn bắt buộc
- Tên đơn vị bán hàng
- Mã số thuế
- Địa chỉ, điện thoại
- Thông tin người mua (nếu có)
- Chi tiết hàng hóa, dịch vụ
- Tổng tiền trước thuế
- Tiền thuế GTGT
- Tổng tiền thanh toán

## Chức năng cần implement

### 1. InvoiceService
```php
class InvoiceService
{
    public function generateInvoiceNumber(): string
    public function calculateTax(array $items): array
    public function createInvoice(array $data): Invoice
    public function sendToTaxAuthority(Invoice $invoice): bool
    public function signDigitally(Invoice $invoice): string
}
```

### 2. TaxCalculator
```php
class TaxCalculator
{
    public function calculateVAT(float $amount, string $taxRate): float
    public function getTaxRateForDrug(Drug $drug): float
    public function calculateTotalWithTax(array $items): array
}
```

### 3. DigitalSignature
```php
class DigitalSignature
{
    public function sign(string $data): string
    public function verify(string $data, string $signature): bool
}
```

## API Endpoints cần tạo
- `POST /api/invoices` - Tạo hóa đơn mới
- `GET /api/invoices/{id}` - Xem chi tiết hóa đơn
- `POST /api/invoices/{id}/send-tax` - Gửi lên cơ quan thuế
- `GET /api/invoices/{id}/pdf` - Xuất PDF hóa đơn
- `POST /api/invoices/{id}/cancel` - Hủy hóa đơn

## Mock API Tổng cục Thuế
```php
// Endpoint giả lập
POST /mock-tax-api/invoices
{
    "invoice_number": "HD202312250001",
    "tax_code": "0123456789",
    "total_amount": 150000,
    "tax_amount": 15000,
    "items": [...]
}

Response:
{
    "status": "success",
    "transaction_id": "TCT123456789",
    "lookup_code": "ABC123XYZ"
}
```

## Validation Rules
- Hóa đơn phải có ít nhất 1 item
- Tổng tiền > 0
- Thông tin khách hàng hợp lệ (nếu có)
- Mã số thuế đúng format (10 số)

## Template hóa đơn
- Hỗ trợ xuất PDF
- Template responsive cho in A4
- QR Code để tra cứu
- Watermark "BẢN CHÍNH" hoặc "BẢN SAO"

## Câu lệnh để bắt đầu
"Hãy tạo InvoiceService với chức năng tính thuế GTGT và sinh mã hóa đơn tự động theo quy định Việt Nam"
