# 🧾 InvoiceAgent - Sales & Invoicing

## Vai trò
Bạn là **InvoiceAgent** chuy<PERSON>n xử lý b<PERSON>, xuất hóa đơn và tính thuế cho hệ thống SaaS hiệu thuốc với Filament 3.

## <PERSON><PERSON>ệ<PERSON> vụ chính
- <PERSON><PERSON><PERSON> hóa đơn bán hàng với Filament forms
- <PERSON><PERSON><PERSON> thuế GTGT tự động (5% thuốc kê đơn, 10% không kê đơn)
- Sinh mã hóa đơn theo quy định Việt Nam
- Xuất PDF hóa đơn và gửi customer portal
- <PERSON><PERSON><PERSON> hợp thanh toán QR code
- B<PERSON><PERSON> cáo doanh thu và thuế

## Invoice Models

### 1. Invoice Model
```php
// app/Models/Invoice.php
class Invoice extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'invoice_number', 'customer_id', 'user_id', 'invoice_date',
        'subtotal', 'discount_amount', 'tax_amount', 'total_amount',
        'payment_method', 'payment_status', 'status', 'uuid',
        'tax_authority_status', 'tax_transaction_id', 'digital_signature',
        'notes', 'qr_code_data'
    ];

    protected $casts = [
        'invoice_date' => 'datetime',
        'subtotal' => 'decimal:2',
        'discount_amount' => 'decimal:2',
        'tax_amount' => 'decimal:2',
        'total_amount' => 'decimal:2',
    ];

    protected static function boot()
    {
        parent::boot();
        
        static::creating(function ($invoice) {
            $invoice->uuid = Str::uuid();
            $invoice->invoice_number = app(InvoiceService::class)->generateInvoiceNumber();
        });
    }

    // Relationships
    public function customer()
    {
        return $this->belongsTo(Customer::class);
    }

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function items()
    {
        return $this->hasMany(InvoiceItem::class);
    }

    // Scopes
    public function scopeConfirmed($query)
    {
        return $query->where('status', 'confirmed');
    }

    public function scopePaid($query)
    {
        return $query->where('payment_status', 'paid');
    }

    public function scopeToday($query)
    {
        return $query->whereDate('invoice_date', today());
    }

    public function scopeThisMonth($query)
    {
        return $query->whereMonth('invoice_date', now()->month)
            ->whereYear('invoice_date', now()->year);
    }

    // Accessors
    public function getCustomerPortalUrlAttribute(): string
    {
        return route('customer.invoice', $this->uuid);
    }

    public function getQrCodeUrlAttribute(): string
    {
        return "https://api.qrserver.com/v1/create-qr-code/?size=200x200&data=" . 
               urlencode($this->customer_portal_url);
    }

    // Business Methods
    public function calculateTotals(): void
    {
        $this->subtotal = $this->items->sum('total_price');
        $this->tax_amount = $this->items->sum('tax_amount');
        $this->total_amount = $this->subtotal + $this->tax_amount - $this->discount_amount;
        $this->save();
    }

    public function confirm(): bool
    {
        if ($this->status !== 'draft') {
            return false;
        }

        DB::transaction(function () {
            // Update stock quantities
            foreach ($this->items as $item) {
                $item->batch->sell($item->quantity);
            }

            // Update customer stats
            if ($this->customer) {
                $this->customer->updatePurchaseStats($this->total_amount);
                $this->customer->earnPoints($this->total_amount);
            }

            // Update invoice status
            $this->update(['status' => 'confirmed']);
        });

        return true;
    }

    public function cancel(): bool
    {
        if ($this->status !== 'confirmed') {
            return false;
        }

        DB::transaction(function () {
            // Restore stock quantities
            foreach ($this->items as $item) {
                $item->batch->increment('current_quantity', $item->quantity);
            }

            // Reverse customer stats
            if ($this->customer) {
                $this->customer->decrement('total_spent', $this->total_amount);
                // Reverse loyalty points
                $pointsToReverse = (int) floor($this->total_amount / 1000);
                $this->customer->decrement('loyalty_points', $pointsToReverse);
            }

            $this->update(['status' => 'cancelled']);
        });

        return true;
    }
}
```

### 2. Invoice Item Model
```php
// app/Models/InvoiceItem.php
class InvoiceItem extends Model
{
    protected $fillable = [
        'invoice_id', 'batch_id', 'quantity', 'unit_price',
        'discount_percentage', 'discount_amount', 'tax_rate',
        'tax_amount', 'total_price'
    ];

    protected $casts = [
        'quantity' => 'integer',
        'unit_price' => 'decimal:2',
        'discount_percentage' => 'decimal:2',
        'discount_amount' => 'decimal:2',
        'tax_rate' => 'decimal:2',
        'tax_amount' => 'decimal:2',
        'total_price' => 'decimal:2',
    ];

    protected static function boot()
    {
        parent::boot();
        
        static::saving(function ($item) {
            $item->calculateAmounts();
        });
    }

    // Relationships
    public function invoice()
    {
        return $this->belongsTo(Invoice::class);
    }

    public function batch()
    {
        return $this->belongsTo(Batch::class);
    }

    // Business Methods
    public function calculateAmounts(): void
    {
        $subtotal = $this->quantity * $this->unit_price;
        $this->discount_amount = $subtotal * ($this->discount_percentage / 100);
        $taxableAmount = $subtotal - $this->discount_amount;
        
        // Get tax rate from drug
        $this->tax_rate = $this->batch->drug->tax_rate * 100; // Convert to percentage
        $this->tax_amount = $taxableAmount * ($this->tax_rate / 100);
        $this->total_price = $taxableAmount + $this->tax_amount;
    }
}
```

## Invoice Services

### 1. Invoice Service
```php
// app/Services/InvoiceService.php
class InvoiceService
{
    public function generateInvoiceNumber(): string
    {
        $date = now()->format('Ymd');
        $sequence = Invoice::whereDate('created_at', today())->count() + 1;
        
        return "HD{$date}" . str_pad($sequence, 4, '0', STR_PAD_LEFT);
    }

    public function createInvoice(array $data): Invoice
    {
        DB::beginTransaction();
        
        try {
            $invoice = Invoice::create([
                'customer_id' => $data['customer_id'] ?? null,
                'user_id' => auth()->id(),
                'invoice_date' => $data['invoice_date'] ?? now(),
                'payment_method' => $data['payment_method'] ?? 'cash',
                'payment_status' => $data['payment_status'] ?? 'paid',
                'status' => 'draft',
                'notes' => $data['notes'] ?? null,
            ]);

            foreach ($data['items'] as $itemData) {
                $batch = Batch::findOrFail($itemData['batch_id']);
                
                // Check stock availability
                if ($batch->current_quantity < $itemData['quantity']) {
                    throw new InsufficientStockException(
                        "Insufficient stock for {$batch->drug->name}. Available: {$batch->current_quantity}"
                    );
                }

                $invoice->items()->create([
                    'batch_id' => $itemData['batch_id'],
                    'quantity' => $itemData['quantity'],
                    'unit_price' => $itemData['unit_price'] ?? $batch->selling_price,
                    'discount_percentage' => $itemData['discount_percentage'] ?? 0,
                ]);
            }

            // Calculate totals
            $invoice->calculateTotals();
            
            // Apply customer discount
            if ($invoice->customer) {
                $customerDiscount = $invoice->customer->calculateDiscount($invoice->subtotal);
                $invoice->update(['discount_amount' => $customerDiscount]);
                $invoice->calculateTotals();
            }

            // Auto-confirm if payment is cash
            if ($data['payment_method'] === 'cash') {
                $invoice->confirm();
            }

            DB::commit();
            return $invoice;
            
        } catch (\Exception $e) {
            DB::rollback();
            throw $e;
        }
    }

    public function generatePDF(Invoice $invoice): string
    {
        $pdf = PDF::loadView('invoices.pdf', compact('invoice'));
        
        $filename = "invoice-{$invoice->invoice_number}.pdf";
        $path = storage_path("app/invoices/{$filename}");
        
        $pdf->save($path);
        
        return $path;
    }

    public function sendToCustomerPortal(Invoice $invoice): bool
    {
        if (!$invoice->customer || !$invoice->customer->phone) {
            return false;
        }

        $message = "Hóa đơn #{$invoice->invoice_number} đã sẵn sàng. Xem tại: {$invoice->customer_portal_url}";
        
        // Send SMS (implement with your SMS provider)
        // SMS::send($invoice->customer->phone, $message);
        
        return true;
    }

    public function sendToTaxAuthority(Invoice $invoice): array
    {
        // Mock implementation - replace with actual tax authority API
        $data = [
            'invoice_number' => $invoice->invoice_number,
            'tax_code' => tenant()->tax_code,
            'customer_name' => $invoice->customer?->name ?? 'Walk-in Customer',
            'customer_tax_code' => $invoice->customer?->tax_code,
            'total_amount' => $invoice->total_amount,
            'tax_amount' => $invoice->tax_amount,
            'items' => $invoice->items->map(function ($item) {
                return [
                    'name' => $item->batch->drug->name,
                    'quantity' => $item->quantity,
                    'unit_price' => $item->unit_price,
                    'tax_rate' => $item->tax_rate,
                ];
            })->toArray(),
        ];

        // Simulate API call
        $response = [
            'status' => 'success',
            'transaction_id' => 'TCT' . Str::random(10),
            'lookup_code' => Str::random(8),
        ];

        $invoice->update([
            'tax_authority_status' => 'sent',
            'tax_transaction_id' => $response['transaction_id'],
        ]);

        return $response;
    }
}
```

### 2. Tax Calculator Service
```php
// app/Services/TaxCalculatorService.php
class TaxCalculatorService
{
    public function calculateVAT(float $amount, Drug $drug): float
    {
        $taxRate = $drug->prescription_required ? 0.05 : 0.10;
        return $amount * $taxRate;
    }

    public function getTaxRate(Drug $drug): float
    {
        return $drug->prescription_required ? 5.0 : 10.0; // Percentage
    }

    public function calculateInvoiceTax(array $items): array
    {
        $totalTax = 0;
        $taxBreakdown = [];

        foreach ($items as $item) {
            $batch = Batch::find($item['batch_id']);
            $drug = $batch->drug;
            
            $subtotal = $item['quantity'] * $item['unit_price'];
            $discount = $subtotal * ($item['discount_percentage'] ?? 0) / 100;
            $taxableAmount = $subtotal - $discount;
            
            $taxRate = $this->getTaxRate($drug);
            $taxAmount = $taxableAmount * ($taxRate / 100);
            
            $totalTax += $taxAmount;
            
            $taxBreakdown[] = [
                'drug_name' => $drug->name,
                'taxable_amount' => $taxableAmount,
                'tax_rate' => $taxRate,
                'tax_amount' => $taxAmount,
            ];
        }

        return [
            'total_tax' => $totalTax,
            'breakdown' => $taxBreakdown,
        ];
    }
}
```

## Filament Resources

### 1. Enhanced Invoice Resource
```php
// app/Filament/Resources/InvoiceResource.php
class InvoiceResource extends Resource
{
    protected static ?string $model = Invoice::class;
    protected static ?string $navigationIcon = 'heroicon-o-document-text';
    protected static ?string $navigationGroup = 'Sales';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Section::make('Invoice Information')
                    ->schema([
                        Grid::make(3)->schema([
                            TextInput::make('invoice_number')
                                ->default(fn () => app(InvoiceService::class)->generateInvoiceNumber())
                                ->disabled()
                                ->dehydrated(),
                            
                            Select::make('customer_id')
                                ->relationship('customer', 'name')
                                ->searchable()
                                ->preload()
                                ->createOptionForm([
                                    TextInput::make('name')->required(),
                                    TextInput::make('phone'),
                                    TextInput::make('email'),
                                ])
                                ->reactive()
                                ->afterStateUpdated(function ($state, callable $set) {
                                    if ($state) {
                                        $customer = Customer::find($state);
                                        $discount = $customer->calculateDiscount(0); // Will be recalculated
                                        $set('customer_discount_rate', $customer->group?->discount_percentage ?? 0);
                                    }
                                }),
                            
                            DateTimePicker::make('invoice_date')
                                ->default(now())
                                ->required(),
                        ]),
                        
                        Grid::make(2)->schema([
                            Select::make('payment_method')
                                ->options([
                                    'cash' => 'Cash',
                                    'card' => 'Card',
                                    'transfer' => 'Bank Transfer',
                                    'qr_code' => 'QR Code',
                                ])
                                ->default('cash')
                                ->required(),
                            
                            Select::make('payment_status')
                                ->options([
                                    'pending' => 'Pending',
                                    'paid' => 'Paid',
                                    'partial' => 'Partial',
                                ])
                                ->default('paid')
                                ->required(),
                        ]),
                    ]),

                Section::make('Invoice Items')
                    ->schema([
                        Repeater::make('items')
                            ->relationship()
                            ->schema([
                                Select::make('batch_id')
                                    ->relationship('batch', 'batch_number')
                                    ->getOptionLabelFromRecordUsing(fn (Batch $record) => 
                                        "{$record->drug->name} - Batch: {$record->batch_number} (Stock: {$record->current_quantity}) - ₫{$record->selling_price}"
                                    )
                                    ->searchable(['batch_number'])
                                    ->preload()
                                    ->required()
                                    ->reactive()
                                    ->afterStateUpdated(function ($state, callable $set) {
                                        if ($state) {
                                            $batch = Batch::find($state);
                                            $set('unit_price', $batch->selling_price);
                                            $set('max_quantity', $batch->current_quantity);
                                            $set('drug_name', $batch->drug->name);
                                            $set('tax_rate', $batch->drug->tax_rate * 100);
                                        }
                                    }),
                                
                                TextInput::make('drug_name')
                                    ->disabled()
                                    ->dehydrated(false),
                                
                                TextInput::make('quantity')
                                    ->numeric()
                                    ->required()
                                    ->minValue(1)
                                    ->reactive()
                                    ->rules([
                                        fn (Get $get): Closure => function (string $attribute, $value, Closure $fail) use ($get) {
                                            $maxQuantity = $get('max_quantity');
                                            if ($value > $maxQuantity) {
                                                $fail("Quantity cannot exceed available stock ({$maxQuantity})");
                                            }
                                        },
                                    ])
                                    ->afterStateUpdated(function ($state, callable $set, callable $get) {
                                        $unitPrice = $get('unit_price') ?? 0;
                                        $discount = $get('discount_percentage') ?? 0;
                                        $taxRate = $get('tax_rate') ?? 0;
                                        
                                        $subtotal = $state * $unitPrice;
                                        $discountAmount = $subtotal * ($discount / 100);
                                        $taxableAmount = $subtotal - $discountAmount;
                                        $taxAmount = $taxableAmount * ($taxRate / 100);
                                        $total = $taxableAmount + $taxAmount;
                                        
                                        $set('total_price', $total);
                                    }),
                                
                                TextInput::make('unit_price')
                                    ->numeric()
                                    ->required()
                                    ->prefix('₫')
                                    ->reactive()
                                    ->afterStateUpdated(function ($state, callable $set, callable $get) {
                                        $quantity = $get('quantity') ?? 0;
                                        $discount = $get('discount_percentage') ?? 0;
                                        $taxRate = $get('tax_rate') ?? 0;
                                        
                                        $subtotal = $quantity * $state;
                                        $discountAmount = $subtotal * ($discount / 100);
                                        $taxableAmount = $subtotal - $discountAmount;
                                        $taxAmount = $taxableAmount * ($taxRate / 100);
                                        $total = $taxableAmount + $taxAmount;
                                        
                                        $set('total_price', $total);
                                    }),
                                
                                TextInput::make('discount_percentage')
                                    ->numeric()
                                    ->suffix('%')
                                    ->minValue(0)
                                    ->maxValue(100)
                                    ->default(0)
                                    ->reactive(),
                                
                                TextInput::make('tax_rate')
                                    ->numeric()
                                    ->suffix('%')
                                    ->disabled()
                                    ->dehydrated(false),
                                
                                TextInput::make('total_price')
                                    ->numeric()
                                    ->prefix('₫')
                                    ->disabled()
                                    ->dehydrated(),
                            ])
                            ->columns(3)
                            ->defaultItems(1)
                            ->addActionLabel('Add Item')
                            ->reorderableWithButtons()
                            ->collapsible()
                            ->cloneable(),
                    ]),

                Section::make('Summary')
                    ->schema([
                        Grid::make(3)->schema([
                            Placeholder::make('subtotal')
                                ->content(function (Get $get): string {
                                    $items = $get('items') ?? [];
                                    $subtotal = collect($items)->sum('total_price');
                                    return '₫' . number_format($subtotal);
                                }),
                            
                            TextInput::make('discount_amount')
                                ->numeric()
                                ->prefix('₫')
                                ->default(0),
                            
                            Placeholder::make('total_amount')
                                ->content(function (Get $get): string {
                                    $items = $get('items') ?? [];
                                    $subtotal = collect($items)->sum('total_price');
                                    $discount = $get('discount_amount') ?? 0;
                                    $total = $subtotal - $discount;
                                    return '₫' . number_format($total);
                                }),
                        ]),
                        
                        Textarea::make('notes')
                            ->maxLength(500)
                            ->rows(3)
                            ->placeholder('Additional notes for this invoice'),
                    ]),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('invoice_number')
                    ->searchable()
                    ->sortable()
                    ->copyable()
                    ->weight(FontWeight::Bold),
                
                TextColumn::make('customer.name')
                    ->searchable()
                    ->sortable()
                    ->placeholder('Walk-in Customer')
                    ->limit(30),
                
                TextColumn::make('invoice_date')
                    ->dateTime('d/m/Y H:i')
                    ->sortable(),
                
                TextColumn::make('total_amount')
                    ->money('VND')
                    ->sortable()
                    ->weight(FontWeight::Bold)
                    ->color('success'),
                
                TextColumn::make('payment_method')
                    ->badge()
                    ->color(fn (string $state): string => match ($state) {
                        'cash' => 'success',
                        'card' => 'info',
                        'transfer' => 'warning',
                        'qr_code' => 'primary',
                        default => 'gray',
                    }),
                
                TextColumn::make('payment_status')
                    ->badge()
                    ->color(fn (string $state): string => match ($state) {
                        'paid' => 'success',
                        'pending' => 'warning',
                        'partial' => 'info',
                        default => 'gray',
                    }),
                
                TextColumn::make('status')
                    ->badge()
                    ->color(fn (string $state): string => match ($state) {
                        'confirmed' => 'success',
                        'draft' => 'warning',
                        'cancelled' => 'danger',
                        default => 'gray',
                    }),
                
                TextColumn::make('user.name')
                    ->label('Cashier')
                    ->toggleable(),
            ])
            ->defaultSort('created_at', 'desc')
            ->filters([
                SelectFilter::make('status')
                    ->options([
                        'draft' => 'Draft',
                        'confirmed' => 'Confirmed',
                        'cancelled' => 'Cancelled',
                    ]),
                
                SelectFilter::make('payment_status')
                    ->options([
                        'pending' => 'Pending',
                        'paid' => 'Paid',
                        'partial' => 'Partial',
                    ]),
                
                SelectFilter::make('payment_method')
                    ->options([
                        'cash' => 'Cash',
                        'card' => 'Card',
                        'transfer' => 'Bank Transfer',
                        'qr_code' => 'QR Code',
                    ]),
                
                Filter::make('created_at')
                    ->form([
                        DatePicker::make('created_from')
                            ->label('From Date'),
                        DatePicker::make('created_until')
                            ->label('To Date'),
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        return $query
                            ->when(
                                $data['created_from'],
                                fn (Builder $query, $date): Builder => $query->whereDate('created_at', '>=', $date),
                            )
                            ->when(
                                $data['created_until'],
                                fn (Builder $query, $date): Builder => $query->whereDate('created_at', '<=', $date),
                            );
                    }),
                
                Filter::make('amount_range')
                    ->form([
                        TextInput::make('amount_from')
                            ->numeric()
                            ->prefix('₫'),
                        TextInput::make('amount_to')
                            ->numeric()
                            ->prefix('₫'),
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        return $query
                            ->when(
                                $data['amount_from'],
                                fn (Builder $query, $amount): Builder => $query->where('total_amount', '>=', $amount),
                            )
                            ->when(
                                $data['amount_to'],
                                fn (Builder $query, $amount): Builder => $query->where('total_amount', '<=', $amount),
                            );
                    }),
            ])
            ->actions([
                Tables\Actions\ActionGroup::make([
                    Tables\Actions\ViewAction::make(),
                    Tables\Actions\EditAction::make()
                        ->visible(fn (Invoice $record) => $record->status === 'draft'),
                    Tables\Actions\Action::make('confirm')
                        ->icon('heroicon-o-check-circle')
                        ->action(fn (Invoice $record) => $record->confirm())
                        ->requiresConfirmation()
                        ->visible(fn (Invoice $record) => $record->status === 'draft')
                        ->color('success'),
                    Tables\Actions\Action::make('cancel')
                        ->icon('heroicon-o-x-circle')
                        ->action(fn (Invoice $record) => $record->cancel())
                        ->requiresConfirmation()
                        ->visible(fn (Invoice $record) => $record->status === 'confirmed')
                        ->color('danger'),
                    Tables\Actions\Action::make('print')
                        ->icon('heroicon-o-printer')
                        ->url(fn (Invoice $record): string => route('invoice.pdf', $record))
                        ->openUrlInNewTab(),
                    Tables\Actions\Action::make('send_customer_link')
                        ->icon('heroicon-o-link')
                        ->action(function (Invoice $record) {
                            app(InvoiceService::class)->sendToCustomerPortal($record);
                        })
                        ->requiresConfirmation()
                        ->visible(fn (Invoice $record) => $record->customer?->phone),
                    Tables\Actions\Action::make('send_tax_authority')
                        ->icon('heroicon-o-building-office')
                        ->action(function (Invoice $record) {
                            app(InvoiceService::class)->sendToTaxAuthority($record);
                        })
                        ->requiresConfirmation()
                        ->visible(fn (Invoice $record) => $record->status === 'confirmed'),
                ]),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                    Tables\Actions\BulkAction::make('confirm_selected')
                        ->action(fn (Collection $records) => 
                            $records->each->confirm()
                        )
                        ->requiresConfirmation()
                        ->icon('heroicon-o-check-circle')
                        ->color('success'),
                    Tables\Actions\BulkAction::make('export_pdf')
                        ->action(function (Collection $records) {
                            // Generate ZIP file with all PDFs
                        })
                        ->icon('heroicon-o-arrow-down-tray'),
                ]),
            ])
            ->headerActions([
                Tables\Actions\Action::make('daily_report')
                    ->icon('heroicon-o-chart-bar')
                    ->url(fn (): string => route('reports.daily-sales'))
                    ->openUrlInNewTab(),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            InvoiceItemsRelationManager::class,
        ];
    }

    public static function getWidgets(): array
    {
        return [
            SalesStatsWidget::class,
        ];
    }
}
```

## Câu lệnh để bắt đầu
"Hãy tạo Invoice system với Filament forms, tính thuế GTGT tự động, sinh mã hóa đơn và customer portal với QR code cho SaaS hiệu thuốc"
