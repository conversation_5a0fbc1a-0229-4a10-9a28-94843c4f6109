# 📖 DocAgent - Documentation

## Vai trò
Bạn là **DocAgent** chuyên tạo documentation toàn diện cho hệ thống SaaS hiệu thuốc multi-tenant.

## Nhiệm vụ chính
- API documentation với OpenAPI/Swagger
- User manual cho Filament admin panel
- Developer guide và architecture docs
- Database schema documentation
- Deployment và maintenance guides
- Video tutorials và screenshots

## Documentation Structure

### 1. API Documentation (OpenAPI 3.0)

#### Swagger Configuration
```php
// config/l5-swagger.php
return [
    'default' => 'default',
    'documentations' => [
        'default' => [
            'api' => [
                'title' => 'PharmaSaaS API Documentation',
                'version' => '1.0.0',
                'description' => 'RESTful API for PharmaSaaS mobile application',
            ],
            'routes' => [
                'api' => 'api/documentation',
            ],
            'paths' => [
                'use_absolute_path' => env('L5_SWAGGER_USE_ABSOLUTE_PATH', true),
                'docs_json' => 'api-docs.json',
                'docs_yaml' => 'api-docs.yaml',
                'format_to_use_for_docs' => env('L5_FORMAT_TO_USE_FOR_DOCS', 'json'),
                'annotations' => [
                    base_path('app/Http/Controllers/Api'),
                    base_path('app/Http/Requests/Api'),
                    base_path('app/Http/Resources'),
                    base_path('app/Models'),
                ],
            ],
        ],
    ],
    'defaults' => [
        'routes' => [
            'docs' => 'docs',
            'oauth2_callback' => 'api/oauth2-callback',
            'middleware' => [
                'api' => [],
                'asset' => [],
                'docs' => [],
                'oauth2_callback' => [],
            ],
            'group_options' => [],
        ],
        'paths' => [
            'docs' => storage_path('api-docs'),
            'views' => base_path('resources/views/vendor/l5-swagger'),
            'base' => env('L5_SWAGGER_BASE_PATH', null),
            'swagger_ui_assets_path' => env('L5_SWAGGER_UI_ASSETS_PATH', 'vendor/swagger-api/swagger-ui/dist/'),
            'excludes' => [],
        ],
        'scanOptions' => [
            'analyser' => null,
            'analysis' => null,
            'processors' => [],
            'pattern' => null,
            'exclude' => [],
        ],
        'securityDefinitions' => [
            'securitySchemes' => [
                'sanctum' => [
                    'type' => 'apiKey',
                    'description' => 'Enter token in format (Bearer <token>)',
                    'name' => 'Authorization',
                    'in' => 'header',
                ],
            ],
            'security' => [
                [
                    'sanctum' => [],
                ],
            ],
        ],
    ],
];
```

#### API Controller Documentation
```php
/**
 * @OA\Info(
 *     title="PharmaSaaS API",
 *     version="1.0.0",
 *     description="RESTful API for PharmaSaaS mobile application",
 *     @OA\Contact(
 *         email="<EMAIL>"
 *     ),
 *     @OA\License(
 *         name="MIT",
 *         url="https://opensource.org/licenses/MIT"
 *     )
 * )
 * 
 * @OA\Server(
 *     url=L5_SWAGGER_CONST_HOST,
 *     description="API Server"
 * )
 * 
 * @OA\SecurityScheme(
 *     securityScheme="sanctum",
 *     type="http",
 *     scheme="bearer",
 *     bearerFormat="JWT"
 * )
 */

/**
 * @OA\Get(
 *     path="/api/v1/drugs",
 *     summary="Get list of drugs",
 *     tags={"Drugs"},
 *     security={{"sanctum":{}}},
 *     @OA\Parameter(
 *         name="search",
 *         in="query",
 *         description="Search by drug name or code",
 *         @OA\Schema(type="string")
 *     ),
 *     @OA\Parameter(
 *         name="page",
 *         in="query",
 *         description="Page number",
 *         @OA\Schema(type="integer", default=1)
 *     ),
 *     @OA\Parameter(
 *         name="per_page",
 *         in="query",
 *         description="Items per page",
 *         @OA\Schema(type="integer", default=20, maximum=100)
 *     ),
 *     @OA\Parameter(
 *         name="prescription_required",
 *         in="query",
 *         description="Filter by prescription requirement",
 *         @OA\Schema(type="boolean")
 *     ),
 *     @OA\Response(
 *         response=200,
 *         description="List of drugs",
 *         @OA\JsonContent(
 *             @OA\Property(property="success", type="boolean", example=true),
 *             @OA\Property(property="message", type="string", example="Drugs retrieved successfully"),
 *             @OA\Property(
 *                 property="data",
 *                 type="array",
 *                 @OA\Items(ref="#/components/schemas/Drug")
 *             ),
 *             @OA\Property(
 *                 property="meta",
 *                 ref="#/components/schemas/PaginationMeta"
 *             )
 *         )
 *     ),
 *     @OA\Response(
 *         response=401,
 *         description="Unauthorized",
 *         @OA\JsonContent(ref="#/components/schemas/ErrorResponse")
 *     )
 * )
 */

/**
 * @OA\Post(
 *     path="/api/v1/invoices",
 *     summary="Create new invoice",
 *     tags={"Invoices"},
 *     security={{"sanctum":{}}},
 *     @OA\RequestBody(
 *         required=true,
 *         @OA\JsonContent(ref="#/components/schemas/CreateInvoiceRequest")
 *     ),
 *     @OA\Response(
 *         response=201,
 *         description="Invoice created successfully",
 *         @OA\JsonContent(
 *             @OA\Property(property="success", type="boolean", example=true),
 *             @OA\Property(property="message", type="string", example="Invoice created successfully"),
 *             @OA\Property(property="data", ref="#/components/schemas/Invoice")
 *         )
 *     ),
 *     @OA\Response(
 *         response=422,
 *         description="Validation error",
 *         @OA\JsonContent(ref="#/components/schemas/ValidationErrorResponse")
 *     )
 * )
 */
```

#### Schema Definitions
```php
/**
 * @OA\Schema(
 *     schema="Drug",
 *     type="object",
 *     title="Drug",
 *     description="Drug model",
 *     @OA\Property(property="id", type="integer", example=1),
 *     @OA\Property(property="code", type="string", example="PAR001"),
 *     @OA\Property(property="name", type="string", example="Paracetamol 500mg"),
 *     @OA\Property(property="generic_name", type="string", example="Paracetamol"),
 *     @OA\Property(property="active_ingredient", type="string", example="Paracetamol 500mg"),
 *     @OA\Property(property="concentration", type="string", example="500mg"),
 *     @OA\Property(property="dosage_form", type="string", example="tablet"),
 *     @OA\Property(property="unit", type="string", example="Box"),
 *     @OA\Property(property="manufacturer", type="string", example="ABC Pharma"),
 *     @OA\Property(property="prescription_required", type="boolean", example=false),
 *     @OA\Property(property="controlled_substance", type="boolean", example=false),
 *     @OA\Property(property="price", type="number", format="float", example=25000),
 *     @OA\Property(property="wholesale_price", type="number", format="float", example=20000),
 *     @OA\Property(property="current_stock", type="integer", example=150),
 *     @OA\Property(property="available_stock", type="integer", example=150),
 *     @OA\Property(property="tax_rate", type="number", format="float", example=0.1),
 *     @OA\Property(property="is_active", type="boolean", example=true),
 *     @OA\Property(property="image_url", type="string", example="https://example.com/drug.jpg"),
 *     @OA\Property(
 *         property="batches",
 *         type="array",
 *         @OA\Items(ref="#/components/schemas/Batch")
 *     ),
 *     @OA\Property(property="created_at", type="string", format="date-time"),
 *     @OA\Property(property="updated_at", type="string", format="date-time")
 * )
 */

/**
 * @OA\Schema(
 *     schema="CreateInvoiceRequest",
 *     type="object",
 *     title="Create Invoice Request",
 *     required={"payment_method", "items"},
 *     @OA\Property(property="customer_id", type="integer", example=1),
 *     @OA\Property(property="payment_method", type="string", enum={"cash", "card", "transfer", "qr_code"}, example="cash"),
 *     @OA\Property(property="payment_status", type="string", enum={"pending", "paid", "partial"}, example="paid"),
 *     @OA\Property(property="notes", type="string", example="Customer notes"),
 *     @OA\Property(
 *         property="items",
 *         type="array",
 *         @OA\Items(
 *             type="object",
 *             required={"batch_id", "quantity", "unit_price"},
 *             @OA\Property(property="batch_id", type="integer", example=1),
 *             @OA\Property(property="quantity", type="integer", example=2),
 *             @OA\Property(property="unit_price", type="number", format="float", example=25000),
 *             @OA\Property(property="discount_percentage", type="number", format="float", example=5)
 *         )
 *     )
 * )
 */

/**
 * @OA\Schema(
 *     schema="PaginationMeta",
 *     type="object",
 *     @OA\Property(property="current_page", type="integer", example=1),
 *     @OA\Property(property="per_page", type="integer", example=20),
 *     @OA\Property(property="total", type="integer", example=150),
 *     @OA\Property(property="last_page", type="integer", example=8),
 *     @OA\Property(property="from", type="integer", example=1),
 *     @OA\Property(property="to", type="integer", example=20)
 * )
 */

/**
 * @OA\Schema(
 *     schema="ErrorResponse",
 *     type="object",
 *     @OA\Property(property="success", type="boolean", example=false),
 *     @OA\Property(property="message", type="string", example="Error message"),
 *     @OA\Property(property="data", type="null"),
 *     @OA\Property(property="errors", type="object")
 * )
 */
```

### 2. User Manual

#### Filament Admin Guide
```markdown
# Hướng dẫn sử dụng PharmaSaaS

## 1. Đăng nhập hệ thống

### Truy cập hệ thống
1. Mở trình duyệt web
2. Truy cập địa chỉ: `https://[ten-hieu-thuoc].pharmasaas.com/admin`
3. Nhập tên đăng nhập và mật khẩu
4. Nhấn "Đăng nhập"

### Quên mật khẩu
1. Nhấn "Quên mật khẩu?" tại trang đăng nhập
2. Nhập email đã đăng ký
3. Kiểm tra email và làm theo hướng dẫn

## 2. Dashboard - Tổng quan

Dashboard hiển thị thông tin tổng quan về hiệu thuốc:

### Thống kê nhanh
- **Doanh thu hôm nay**: Tổng tiền bán được trong ngày
- **Tổng số thuốc**: Số lượng thuốc đang quản lý
- **Hàng sắp hết**: Thuốc có tồn kho thấp (< 10)
- **Sắp hết hạn**: Thuốc hết hạn trong 30 ngày tới

### Biểu đồ doanh thu
- Hiển thị doanh thu 30 ngày gần nhất
- Có thể thay đổi khoảng thời gian xem

### Cảnh báo quan trọng
- Thuốc sắp hết hạn
- Thuốc hết tồn kho
- Hóa đơn chưa thanh toán

## 3. Quản lý thuốc

### Thêm thuốc mới
1. Vào menu "Kho hàng" → "Thuốc"
2. Nhấn "Thêm thuốc mới"
3. Điền thông tin:
   - **Mã thuốc**: Mã duy nhất (VD: PAR001)
   - **Tên thuốc**: Tên đầy đủ (VD: Paracetamol 500mg)
   - **Hoạt chất**: Thành phần chính
   - **Dạng bào chế**: Viên, chai, ống...
   - **Đơn vị tính**: Hộp, chai, tuýp...
   - **Nhà sản xuất**: Tên công ty sản xuất
   - **Giá bán**: Giá bán lẻ
   - **Kê đơn**: Có yêu cầu đơn thuốc không
4. Nhấn "Lưu"

### Quản lý lô hàng
1. Vào chi tiết thuốc
2. Tab "Lô hàng"
3. Nhấn "Thêm lô mới"
4. Điền thông tin:
   - **Số lô**: Số lô từ nhà sản xuất
   - **Ngày sản xuất**: Ngày sản xuất
   - **Hạn sử dụng**: Ngày hết hạn
   - **Số lượng**: Số lượng nhập
   - **Giá nhập**: Giá mua từ nhà cung cấp
   - **Giá bán**: Giá bán ra

### Tìm kiếm và lọc thuốc
- **Tìm kiếm**: Gõ tên thuốc hoặc mã thuốc
- **Lọc theo dạng**: Viên, chai, ống...
- **Lọc theo kê đơn**: Thuốc kê đơn/không kê đơn
- **Lọc theo trạng thái**: Đang bán/ngừng bán
- **Hàng sắp hết**: Chỉ hiện thuốc tồn kho thấp

## 4. Bán hàng

### Tạo hóa đơn mới
1. Vào menu "Bán hàng" → "Hóa đơn"
2. Nhấn "Tạo hóa đơn mới"
3. Chọn khách hàng (hoặc để trống nếu khách vãng lai)
4. Thêm thuốc:
   - Tìm và chọn lô thuốc
   - Nhập số lượng
   - Kiểm tra giá bán
   - Áp dụng giảm giá (nếu có)
5. Chọn phương thức thanh toán
6. Nhấn "Tạo hóa đơn"

### Xử lý thuốc kê đơn
1. Yêu cầu đơn thuốc từ bác sĩ
2. Kiểm tra thông tin bệnh nhân
3. Tư vấn cách sử dụng
4. Ghi chú đặc biệt (nếu có)
5. Tạo hóa đơn như bình thường

### In hóa đơn
1. Sau khi tạo hóa đơn thành công
2. Nhấn "In hóa đơn"
3. Chọn máy in
4. Kiểm tra và in

## 5. Quản lý khách hàng

### Thêm khách hàng mới
1. Vào menu "Khách hàng"
2. Nhấn "Thêm khách hàng"
3. Điền thông tin:
   - **Tên**: Họ tên đầy đủ
   - **Số điện thoại**: Để liên hệ
   - **Email**: Để gửi hóa đơn
   - **Địa chỉ**: Địa chỉ nhà
   - **Nhóm khách hàng**: Lẻ/Sỉ/VIP
4. Nhấn "Lưu"

### Hệ thống tích điểm
- **Tích điểm**: 1000 VND = 1 điểm
- **Đổi điểm**: 100 điểm = 1000 VND giảm giá
- **Hạn sử dụng**: 12 tháng
- **Không tích điểm**: Thuốc kê đơn

### Nâng hạng tự động
- **VIP**: Mua > 10 triệu/năm
- **Sỉ**: Doanh nghiệp + mua > 5 triệu/tháng

## 6. Báo cáo

### Báo cáo doanh thu
1. Vào menu "Báo cáo" → "Doanh thu"
2. Chọn khoảng thời gian
3. Xem biểu đồ và bảng số liệu
4. Xuất Excel nếu cần

### Báo cáo tồn kho
1. Vào menu "Báo cáo" → "Tồn kho"
2. Xem danh sách thuốc và số lượng
3. Lọc theo nhóm thuốc
4. Xuất báo cáo

### Báo cáo khách hàng
1. Vào menu "Báo cáo" → "Khách hàng"
2. Xem top khách hàng mua nhiều
3. Phân tích RFM
4. Báo cáo tích điểm

## 7. Cài đặt

### Thông tin hiệu thuốc
1. Vào menu "Cài đặt" → "Thông tin hiệu thuốc"
2. Cập nhật:
   - Tên hiệu thuốc
   - Địa chỉ
   - Số điện thoại
   - Mã số thuế
   - Logo

### Quản lý nhân viên
1. Vào menu "Cài đặt" → "Nhân viên"
2. Thêm/sửa/xóa tài khoản nhân viên
3. Phân quyền theo vai trò:
   - **Chủ hiệu thuốc**: Toàn quyền
   - **Quản lý**: Xem báo cáo, quản lý nhân viên
   - **Dược sĩ**: Bán thuốc kê đơn
   - **Thu ngân**: Bán thuốc thường
   - **Thủ kho**: Quản lý kho

## 8. Xử lý sự cố

### Quên mật khẩu
1. Dùng chức năng "Quên mật khẩu"
2. Hoặc liên hệ quản trị viên

### Lỗi kết nối
1. Kiểm tra kết nối internet
2. Thử tải lại trang (F5)
3. Xóa cache trình duyệt

### Hỗ trợ kỹ thuật
- **Hotline**: 1900-xxxx
- **Email**: <EMAIL>
- **Chat**: Góc phải màn hình
- **Thời gian**: 8h-22h hàng ngày
```

### 3. Developer Guide

#### Architecture Documentation
```markdown
# PharmaSaaS Developer Guide

## Architecture Overview

PharmaSaaS is a multi-tenant SaaS application built with:
- **Backend**: Laravel 11 + Filament 3
- **Frontend**: Blade + Livewire + Tailwind CSS
- **Mobile**: Flutter (separate app)
- **Database**: MySQL 8.0 (database per tenant)
- **Cache**: Redis
- **Queue**: Laravel Queue + Redis
- **Storage**: Local/S3

## Multi-Tenancy Strategy

### Database Per Tenant
Each pharmacy gets its own database:
```
Central Database:
├── tenants
├── users (platform users)
├── subscriptions
└── system_settings

Tenant Databases:
pharmacy1_db: drugs, customers, invoices, staff
pharmacy2_db: drugs, customers, invoices, staff
```

### Tenant Initialization
```php
// Automatic tenant detection
Route::middleware([
    InitializeTenancyByDomain::class,
    PreventAccessFromCentralDomains::class,
])->group(function () {
    // Tenant routes
});
```

## Development Setup

### Requirements
- PHP 8.2+
- Composer
- Node.js 18+
- MySQL 8.0
- Redis

### Installation
```bash
git clone https://github.com/company/pharmasaas.git
cd pharmasaas
composer install
npm install
cp .env.example .env
php artisan key:generate
php artisan migrate
php artisan db:seed
npm run dev
php artisan serve
```

### Environment Variables
```env
APP_NAME="PharmaSaaS"
APP_ENV=local
APP_DEBUG=true
APP_URL=http://localhost:8000

DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=pharmasaas_central
DB_USERNAME=root
DB_PASSWORD=

TENANCY_DATABASE_AUTO_DELETE=false
TENANCY_DATABASE_AUTO_DELETE_USER=false

REDIS_HOST=127.0.0.1
REDIS_PASSWORD=null
REDIS_PORT=6379

QUEUE_CONNECTION=redis
```

## Code Standards

### PHP (PSR-12)
- Use strict types: `declare(strict_types=1);`
- Type hints for all parameters and return types
- Proper docblocks with @param and @return
- Repository pattern for data access
- Service classes for business logic

### Database
- Snake_case for table and column names
- Proper foreign key constraints
- Indexes for frequently queried columns
- Soft deletes for important entities

### API Design
- RESTful endpoints
- Consistent response format
- Proper HTTP status codes
- Pagination for list endpoints
- Filtering and sorting support

## Testing

### Running Tests
```bash
# All tests
php artisan test

# Specific test suite
php artisan test --testsuite=Feature
php artisan test --testsuite=Unit

# With coverage
php artisan test --coverage

# Parallel testing
php artisan test --parallel
```

### Test Structure
```
tests/
├── Unit/           # Unit tests
├── Feature/        # Feature tests
├── Browser/        # Browser tests (Dusk)
└── Performance/    # Performance tests
```

## Deployment

### Production Requirements
- Ubuntu 20.04 LTS
- Nginx 1.18+
- PHP 8.2 with FPM
- MySQL 8.0
- Redis 6.0+
- SSL Certificate

### Deployment Steps
1. Setup server environment
2. Configure database
3. Deploy application code
4. Run migrations
5. Configure web server
6. Setup SSL
7. Configure monitoring

### CI/CD Pipeline
```yaml
# .github/workflows/deploy.yml
name: Deploy to Production

on:
  push:
    branches: [main]

jobs:
  deploy:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - name: Setup PHP
        uses: shivammathur/setup-php@v2
        with:
          php-version: 8.2
      - name: Install dependencies
        run: composer install --no-dev --optimize-autoloader
      - name: Run tests
        run: php artisan test
      - name: Deploy to server
        run: # Deployment script
```

## Contributing

### Git Workflow
```bash
git checkout -b feature/new-feature
# Make changes
git add .
git commit -m "feat: add new feature"
git push origin feature/new-feature
# Create Pull Request
```

### Commit Messages
Follow conventional commits:
- `feat:` New feature
- `fix:` Bug fix
- `docs:` Documentation
- `style:` Code style
- `refactor:` Code refactoring
- `test:` Tests
- `chore:` Maintenance

### Code Review Checklist
- [ ] Code follows PSR-12 standards
- [ ] All tests pass
- [ ] Documentation updated
- [ ] No security vulnerabilities
- [ ] Performance considerations
- [ ] Multi-tenant compatibility
```

### 4. Database Schema Documentation

#### ERD Generator
```php
// app/Console/Commands/GenerateERD.php
class GenerateERD extends Command
{
    protected $signature = 'docs:erd';
    protected $description = 'Generate Entity Relationship Diagram';
    
    public function handle()
    {
        $tables = DB::select('SHOW TABLES');
        $relationships = [];
        
        foreach ($tables as $table) {
            $tableName = array_values((array) $table)[0];
            $columns = DB::select("DESCRIBE {$tableName}");
            $foreignKeys = DB::select("
                SELECT 
                    COLUMN_NAME,
                    REFERENCED_TABLE_NAME,
                    REFERENCED_COLUMN_NAME
                FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE 
                WHERE TABLE_NAME = '{$tableName}' 
                AND REFERENCED_TABLE_NAME IS NOT NULL
            ");
            
            // Generate Mermaid ERD syntax
            $this->generateMermaidERD($tableName, $columns, $foreignKeys);
        }
    }
    
    private function generateMermaidERD($table, $columns, $foreignKeys)
    {
        // Implementation for Mermaid ERD generation
    }
}
```

## Câu lệnh để bắt đầu
"Hãy tạo comprehensive documentation với OpenAPI/Swagger cho API, user manual cho Filament admin, developer guide và database schema docs cho SaaS hiệu thuốc"
