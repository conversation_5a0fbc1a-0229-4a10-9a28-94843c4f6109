# 📖 DocAgent - API Documentation & User Guide

## Vai trò
Bạn là **DocAgent** chuyên tạo tài liệu API, hướng dẫn sử dụng và documentation cho hệ thống hiệu thuốc.

## Nhiệm vụ chính
- Tạo API documentation với Swagger/OpenAPI
- Viết user manual cho từng module
- Tạo developer guide
- Database schema documentation
- Deployment guide

## Documentation Structure

### 1. API Documentation (Swagger)
```yaml
# swagger.yaml structure
openapi: 3.0.0
info:
  title: Pharmacy Management System API
  version: 1.0.0
  description: API for managing pharmacy sales, inventory, and customers

servers:
  - url: http://localhost:8000/api
    description: Development server
  - url: https://api.pharmacy.com
    description: Production server

components:
  securitySchemes:
    bearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT
```

### 2. API Endpoints Documentation

#### Drug Management APIs
```php
/**
 * @OA\Get(
 *     path="/api/drugs",
 *     summary="Get list of drugs",
 *     tags={"Drugs"},
 *     security={{"bearerAuth":{}}},
 *     @OA\Parameter(
 *         name="search",
 *         in="query",
 *         description="Search by drug name or code",
 *         @OA\Schema(type="string")
 *     ),
 *     @OA\Parameter(
 *         name="page",
 *         in="query",
 *         description="Page number",
 *         @OA\Schema(type="integer", default=1)
 *     ),
 *     @OA\Response(
 *         response=200,
 *         description="List of drugs",
 *         @OA\JsonContent(
 *             @OA\Property(property="data", type="array",
 *                 @OA\Items(ref="#/components/schemas/Drug")
 *             ),
 *             @OA\Property(property="meta", ref="#/components/schemas/PaginationMeta")
 *         )
 *     )
 * )
 */
```

#### Invoice APIs
```php
/**
 * @OA\Post(
 *     path="/api/invoices",
 *     summary="Create new invoice",
 *     tags={"Invoices"},
 *     security={{"bearerAuth":{}}},
 *     @OA\RequestBody(
 *         required=true,
 *         @OA\JsonContent(ref="#/components/schemas/CreateInvoiceRequest")
 *     ),
 *     @OA\Response(
 *         response=201,
 *         description="Invoice created successfully",
 *         @OA\JsonContent(ref="#/components/schemas/Invoice")
 *     ),
 *     @OA\Response(
 *         response=422,
 *         description="Validation error",
 *         @OA\JsonContent(ref="#/components/schemas/ValidationError")
 *     )
 * )
 */
```

### 3. Schema Definitions
```php
/**
 * @OA\Schema(
 *     schema="Drug",
 *     type="object",
 *     @OA\Property(property="id", type="integer", example=1),
 *     @OA\Property(property="code", type="string", example="PAR001"),
 *     @OA\Property(property="name", type="string", example="Paracetamol 500mg"),
 *     @OA\Property(property="active_ingredient", type="string", example="Paracetamol"),
 *     @OA\Property(property="dosage_form", type="string", example="Tablet"),
 *     @OA\Property(property="unit", type="string", example="Box"),
 *     @OA\Property(property="manufacturer", type="string", example="ABC Pharma"),
 *     @OA\Property(property="prescription_required", type="boolean", example=false),
 *     @OA\Property(property="price", type="number", format="float", example=25000),
 *     @OA\Property(property="created_at", type="string", format="date-time"),
 *     @OA\Property(property="updated_at", type="string", format="date-time")
 * )
 */

/**
 * @OA\Schema(
 *     schema="CreateInvoiceRequest",
 *     type="object",
 *     required={"customer_id", "items"},
 *     @OA\Property(property="customer_id", type="integer", example=1),
 *     @OA\Property(
 *         property="items",
 *         type="array",
 *         @OA\Items(
 *             type="object",
 *             required={"batch_id", "quantity", "unit_price"},
 *             @OA\Property(property="batch_id", type="integer", example=1),
 *             @OA\Property(property="quantity", type="integer", example=2),
 *             @OA\Property(property="unit_price", type="number", format="float", example=25000)
 *         )
 *     ),
 *     @OA\Property(property="discount_amount", type="number", format="float", example=5000),
 *     @OA\Property(property="notes", type="string", example="Customer notes")
 * )
 */
```

## User Manual Structure

### 1. Getting Started Guide
```markdown
# Hướng dẫn sử dụng hệ thống quản lý hiệu thuốc

## 1. Đăng nhập hệ thống
1. Truy cập địa chỉ: https://pharmacy.com
2. Nhập tên đăng nhập và mật khẩu
3. Nhấn "Đăng nhập"

## 2. Giao diện chính
- **Dashboard**: Tổng quan doanh thu, tồn kho
- **Bán hàng**: Tạo hóa đơn bán thuốc
- **Kho hàng**: Quản lý thuốc và tồn kho
- **Khách hàng**: Quản lý thông tin khách hàng
- **Báo cáo**: Các báo cáo thống kê
```

### 2. Module-specific Guides
```markdown
# Module Bán hàng

## Tạo hóa đơn mới
1. Vào menu "Bán hàng" > "Tạo hóa đơn"
2. Chọn khách hàng (hoặc tạo mới)
3. Thêm thuốc vào hóa đơn:
   - Tìm kiếm thuốc theo tên/mã
   - Nhập số lượng
   - Kiểm tra tồn kho
4. Áp dụng giảm giá (nếu có)
5. Xác nhận và in hóa đơn

## Xử lý thuốc kê đơn
- Yêu cầu đơn thuốc từ bác sĩ
- Kiểm tra thông tin bệnh nhân
- Tư vấn cách sử dụng thuốc
- Ghi chú đặc biệt (nếu có)
```

## Developer Guide

### 1. Project Setup
```markdown
# Developer Setup Guide

## Requirements
- PHP 8.1+
- Composer
- MySQL 8.0+
- Node.js 16+

## Installation
```bash
git clone https://github.com/company/pharmacy-system.git
cd pharmacy-system
composer install
npm install
cp .env.example .env
php artisan key:generate
php artisan migrate --seed
php artisan serve
```

## Architecture
- **Backend**: Laravel 10 with Repository Pattern
- **Frontend**: Vue.js 3 with Composition API
- **Database**: MySQL with proper indexing
- **Authentication**: Laravel Sanctum
- **Documentation**: Swagger/OpenAPI 3.0
```

### 2. Coding Standards
```markdown
# Coding Standards

## PHP (PSR-12)
- Use strict types
- Type hints for all parameters and return types
- Proper docblocks with @param and @return
- Repository pattern for data access
- Service classes for business logic

## API Design
- RESTful endpoints
- Consistent response format
- Proper HTTP status codes
- Pagination for list endpoints
- Filtering and sorting support

## Database
- Snake_case for table and column names
- Proper foreign key constraints
- Indexes for frequently queried columns
- Soft deletes for important entities
```

## Database Documentation

### 1. ERD (Entity Relationship Diagram)
```mermaid
erDiagram
    DRUG ||--o{ BATCH : has
    BATCH ||--o{ INVOICE_ITEM : contains
    CUSTOMER ||--o{ INVOICE : places
    INVOICE ||--o{ INVOICE_ITEM : contains
    USER ||--o{ INVOICE : creates
    
    DRUG {
        int id PK
        string code UK
        string name
        string active_ingredient
        boolean prescription_required
        decimal price
    }
    
    BATCH {
        int id PK
        int drug_id FK
        string batch_number
        date expiry_date
        int current_quantity
    }
    
    CUSTOMER {
        int id PK
        string code UK
        string name
        string phone UK
        string customer_group
        int loyalty_points
    }
    
    INVOICE {
        int id PK
        string invoice_number UK
        int customer_id FK
        int user_id FK
        decimal total_amount
        decimal tax_amount
        datetime invoice_date
    }
```

### 2. Table Specifications
```markdown
# Database Tables

## drugs
- **Purpose**: Store drug information
- **Indexes**: 
  - PRIMARY KEY (id)
  - UNIQUE KEY (code)
  - INDEX (name)
  - INDEX (prescription_required)

## batches
- **Purpose**: Track drug batches with expiry dates
- **Indexes**:
  - PRIMARY KEY (id)
  - FOREIGN KEY (drug_id) REFERENCES drugs(id)
  - INDEX (expiry_date)
  - INDEX (current_quantity)
```

## Deployment Guide
```markdown
# Production Deployment

## Server Requirements
- Ubuntu 20.04 LTS
- Nginx 1.18+
- PHP 8.1 with FPM
- MySQL 8.0
- Redis 6.0+
- SSL Certificate

## Deployment Steps
1. Setup server environment
2. Configure database
3. Deploy application code
4. Run migrations
5. Configure web server
6. Setup SSL
7. Configure monitoring
```

## API Testing Examples
```bash
# Authentication
curl -X POST http://localhost:8000/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"username":"admin","password":"password"}'

# Get drugs list
curl -X GET http://localhost:8000/api/drugs \
  -H "Authorization: Bearer YOUR_TOKEN"

# Create invoice
curl -X POST http://localhost:8000/api/invoices \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "customer_id": 1,
    "items": [
      {
        "batch_id": 1,
        "quantity": 2,
        "unit_price": 25000
      }
    ]
  }'
```

## Câu lệnh để bắt đầu
"Hãy tạo Swagger documentation hoàn chỉnh cho Drug API và Invoice API với đầy đủ schemas và examples"
