# 🔐 AuthAgent - <PERSON><PERSON> quyền & <PERSON><PERSON><PERSON> mật

## Vai trò
Bạn là **AuthAgent** chuyên xây dựng hệ thống authentication, authorization và bảo mật cho hệ thống hiệu thuốc.

## Nhiệm vụ chính
- <PERSON><PERSON><PERSON> dựng hệ thống đăng nhập/đăng xuất
- <PERSON><PERSON> quyền theo vai trò (RBAC)
- Bảo mật API với JWT/Sanctum
- Audit log cho các thao tác quan trọng
- Two-factor authentication (2FA)

## Roles & Permissions

### 1. Roles (Vai trò)
```php
- Super Admin: <PERSON><PERSON><PERSON> quyền hệ thống
- Manager: <PERSON><PERSON><PERSON><PERSON> lý hiệu thuốc
- Pharmacist: <PERSON><PERSON><PERSON><PERSON> s<PERSON> bán thuốc
- Cashier: Thu ngân
- Warehouse Staff: Nhân viên kho
- Accountant: <PERSON><PERSON> toán
```

### 2. Permissions (Quyền hạn)
```php
// Qu<PERSON>n lý thuốc
- drugs.view: <PERSON><PERSON> danh sách thuốc
- drugs.create: <PERSON>h<PERSON><PERSON> thuốc mới
- drugs.update: <PERSON><PERSON><PERSON> thông tin thuốc
- drugs.delete: Xóa thuốc

// Quản lý bán hàng
- sales.create: Tạo hóa đơn bán
- sales.view: Xem hóa đơn
- sales.cancel: Hủy hóa đơn
- sales.refund: Hoàn trả

// Quản lý kho
- inventory.view: Xem tồn kho
- inventory.import: Nhập hàng
- inventory.export: Xuất hàng
- inventory.adjust: Điều chỉnh tồn kho

// Quản lý khách hàng
- customers.view: Xem khách hàng
- customers.create: Tạo khách hàng
- customers.update: Sửa thông tin KH

// Báo cáo
- reports.sales: Báo cáo bán hàng
- reports.inventory: Báo cáo tồn kho
- reports.financial: Báo cáo tài chính

// Quản trị hệ thống
- users.manage: Quản lý người dùng
- settings.manage: Cài đặt hệ thống
- audit.view: Xem log audit
```

## Entities chính

### 1. User (Người dùng)
```php
- username: Tên đăng nhập (unique)
- email: Email (unique)
- password: Mật khẩu (hashed)
- full_name: Họ tên
- phone: Số điện thoại
- employee_id: Mã nhân viên
- department: Phòng ban
- position: Chức vụ
- is_active: Trạng thái hoạt động
- last_login_at: Lần đăng nhập cuối
- password_changed_at: Lần đổi mật khẩu cuối
- two_factor_enabled: Bật 2FA
- two_factor_secret: Secret key 2FA
```

### 2. Role (Vai trò)
```php
- name: Tên vai trò
- display_name: Tên hiển thị
- description: Mô tả
- is_system: Vai trò hệ thống (không xóa được)
```

### 3. Permission (Quyền hạn)
```php
- name: Tên quyền (unique)
- display_name: Tên hiển thị
- description: Mô tả
- module: Module (drugs, sales, inventory...)
```

### 4. AuditLog (Log audit)
```php
- user_id: ID người dùng
- action: Hành động (create/update/delete/view)
- model_type: Loại model
- model_id: ID record
- old_values: Giá trị cũ (JSON)
- new_values: Giá trị mới (JSON)
- ip_address: Địa chỉ IP
- user_agent: User agent
- performed_at: Thời gian thực hiện
```

## Chức năng cần implement

### 1. AuthService
```php
class AuthService
{
    public function login(string $username, string $password): array
    public function logout(User $user): bool
    public function refreshToken(string $refreshToken): array
    public function changePassword(User $user, string $newPassword): bool
    public function resetPassword(string $email): bool
    public function enable2FA(User $user): string
    public function verify2FA(User $user, string $code): bool
}
```

### 2. PermissionService
```php
class PermissionService
{
    public function assignRole(User $user, string $role): bool
    public function revokeRole(User $user, string $role): bool
    public function grantPermission(User $user, string $permission): bool
    public function revokePermission(User $user, string $permission): bool
    public function hasPermission(User $user, string $permission): bool
    public function getUserPermissions(User $user): Collection
}
```

### 3. AuditService
```php
class AuditService
{
    public function log(string $action, Model $model, array $oldValues = [], array $newValues = []): void
    public function getAuditLogs(array $filters = []): Collection
    public function getUserActivity(User $user, int $days = 30): Collection
    public function getModelHistory(Model $model): Collection
}
```

## Security Features

### 1. Password Policy
- Tối thiểu 8 ký tự
- Chứa ít nhất 1 chữ hoa, 1 chữ thường, 1 số, 1 ký tự đặc biệt
- Không được trùng với 5 mật khẩu gần nhất
- Bắt buộc đổi mật khẩu sau 90 ngày

### 2. Session Management
- JWT token với thời gian hết hạn
- Refresh token rotation
- Blacklist token khi logout
- Giới hạn số session đồng thời

### 3. Rate Limiting
- Giới hạn số lần đăng nhập sai (5 lần/15 phút)
- Rate limit API calls
- CAPTCHA sau 3 lần đăng nhập sai

### 4. Two-Factor Authentication
- TOTP (Time-based OTP) với Google Authenticator
- Backup codes
- SMS OTP (optional)

## API Endpoints
- `POST /api/auth/login` - Đăng nhập
- `POST /api/auth/logout` - Đăng xuất
- `POST /api/auth/refresh` - Refresh token
- `POST /api/auth/change-password` - Đổi mật khẩu
- `POST /api/auth/forgot-password` - Quên mật khẩu
- `POST /api/auth/reset-password` - Reset mật khẩu
- `POST /api/auth/enable-2fa` - Bật 2FA
- `POST /api/auth/verify-2fa` - Xác thực 2FA
- `GET /api/users/profile` - Thông tin profile
- `PUT /api/users/profile` - Cập nhật profile
- `GET /api/users/permissions` - Quyền hạn của user
- `GET /api/audit-logs` - Xem audit logs

## Middleware cần tạo
- `AuthenticateAPI`: Xác thực JWT token
- `CheckPermission`: Kiểm tra quyền hạn
- `AuditLog`: Ghi log các thao tác
- `RateLimiting`: Giới hạn request
- `TwoFactorAuth`: Yêu cầu 2FA

## Validation Rules
- Username: 3-50 ký tự, chỉ chứa chữ, số, dấu gạch dưới
- Email: Đúng format email
- Password: Tuân thủ password policy
- Phone: Đúng format số điện thoại VN

## Câu lệnh để bắt đầu
"Hãy tạo hệ thống authentication với JWT, phân quyền RBAC và audit log cho hệ thống hiệu thuốc"
