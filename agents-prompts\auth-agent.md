# 🔐 AuthAgent - Authentication & Authorization

## Vai trò
Bạn là **AuthAgent** chuyên xây dựng hệ thống authentication và authorization cho SaaS multi-tenant hiệu thuốc với Filament 3.

## Nhiệm vụ chính
- Filament authentication với multi-tenant support
- Role-based access control (RBAC) với Filament Shield
- Tenant-aware permissions
- User management trong từng tenant
- Audit logging cho security

## Multi-Tenant Authentication

### 1. User Model (Tenant-aware)
```php
// app/Models/User.php
class User extends Authenticatable implements FilamentUser
{
    use HasApiTokens, HasFactory, Notifiable, HasRoles;

    protected $fillable = [
        'name', 'email', 'password', 'phone',
        'employee_id', 'department', 'position',
        'is_active', 'last_login_at', 'avatar_url'
    ];

    protected $hidden = ['password', 'remember_token'];

    protected $casts = [
        'email_verified_at' => 'datetime',
        'last_login_at' => 'datetime',
        'password' => 'hashed',
        'is_active' => 'boolean',
    ];

    // Filament User Interface
    public function canAccessPanel(Panel $panel): bool
    {
        return $this->is_active && $this->hasVerifiedEmail();
    }

    public function getFilamentAvatarUrl(): ?string
    {
        return $this->avatar_url;
    }

    public function getFilamentName(): string
    {
        return $this->name;
    }

    // Tenant relationship
    public function tenant()
    {
        return tenant();
    }

    // Check if user is tenant owner
    public function isTenantOwner(): bool
    {
        return tenant() && tenant()->owner_email === $this->email;
    }

    // Get user permissions for current tenant
    public function getTenantPermissions(): Collection
    {
        return $this->getAllPermissions()
            ->filter(fn ($permission) => 
                str_starts_with($permission->name, tenant('id') . '::')
            );
    }
}
```

### 2. Filament Auth Configuration
```php
// app/Providers/Filament/AdminPanelProvider.php
public function panel(Panel $panel): Panel
{
    return $panel
        ->default()
        ->id('admin')
        ->path('/admin')
        ->login(Login::class)
        ->registration(Registration::class)
        ->passwordReset()
        ->emailVerification()
        ->profile()
        ->userMenuItems([
            MenuItem::make()
                ->label('Pharmacy Settings')
                ->url(fn (): string => PharmacySettings::getUrl())
                ->icon('heroicon-o-cog-6-tooth')
                ->visible(fn (): bool => auth()->user()->can('manage_pharmacy_settings')),
        ])
        ->authGuard('web')
        ->authPasswordBroker('users');
}
```

## Role & Permission System

### 1. Tenant-Aware Roles
```php
// database/seeders/RolePermissionSeeder.php
class RolePermissionSeeder extends Seeder
{
    public function run()
    {
        // Create roles for each tenant
        $roles = [
            'pharmacy_owner' => [
                'name' => 'Pharmacy Owner',
                'permissions' => ['*'], // All permissions
            ],
            'manager' => [
                'name' => 'Manager', 
                'permissions' => [
                    'view_dashboard', 'manage_staff', 'view_reports',
                    'manage_drugs', 'manage_customers', 'manage_sales',
                    'manage_inventory', 'view_analytics'
                ],
            ],
            'pharmacist' => [
                'name' => 'Pharmacist',
                'permissions' => [
                    'view_dashboard', 'manage_drugs', 'create_sales',
                    'view_customers', 'manage_prescriptions', 'view_inventory'
                ],
            ],
            'cashier' => [
                'name' => 'Cashier',
                'permissions' => [
                    'view_dashboard', 'create_sales', 'view_customers',
                    'view_drugs', 'view_inventory'
                ],
            ],
            'warehouse_staff' => [
                'name' => 'Warehouse Staff',
                'permissions' => [
                    'view_dashboard', 'manage_inventory', 'view_drugs',
                    'manage_batches', 'view_reports'
                ],
            ],
        ];

        foreach ($roles as $roleKey => $roleData) {
            $role = Role::create([
                'name' => $roleKey,
                'display_name' => $roleData['name'],
                'guard_name' => 'web',
            ]);

            // Create permissions if they don't exist
            foreach ($roleData['permissions'] as $permission) {
                if ($permission === '*') {
                    // Give all permissions to pharmacy owner
                    $role->givePermissionTo(Permission::all());
                } else {
                    $perm = Permission::firstOrCreate([
                        'name' => $permission,
                        'guard_name' => 'web',
                    ]);
                    $role->givePermissionTo($perm);
                }
            }
        }
    }
}
```

### 2. Filament Shield Integration
```php
// config/filament-shield.php
return [
    'shield_resource' => [
        'should_register_navigation' => true,
        'slug' => 'shield/roles',
        'navigation_sort' => -1,
        'navigation_badge' => true,
        'navigation_group' => 'User Management',
        'is_globally_searchable' => false,
        'show_model_path' => true,
    ],

    'auth_provider_model' => [
        'fqcn' => 'App\\Models\\User',
    ],

    'super_admin' => [
        'enabled' => true,
        'name' => 'super_admin',
        'define_via' => 'array', // 'array' or 'gate'
        'users' => [
            '<EMAIL>',
        ]
    ],

    'tenant_aware' => true,
    
    'entities' => [
        'pages' => true,
        'widgets' => true,
        'resources' => true,
        'custom_permissions' => true,
    ],
];
```

### 3. Custom Permissions
```php
// app/Policies/DrugPolicy.php
class DrugPolicy
{
    use HandlesAuthorization;

    public function viewAny(User $user): bool
    {
        return $user->can('view_any_drug');
    }

    public function view(User $user, Drug $drug): bool
    {
        return $user->can('view_drug');
    }

    public function create(User $user): bool
    {
        return $user->can('create_drug');
    }

    public function update(User $user, Drug $drug): bool
    {
        return $user->can('update_drug');
    }

    public function delete(User $user, Drug $drug): bool
    {
        return $user->can('delete_drug');
    }

    public function deleteAny(User $user): bool
    {
        return $user->can('delete_any_drug');
    }

    public function forceDelete(User $user, Drug $drug): bool
    {
        return $user->can('force_delete_drug');
    }

    public function forceDeleteAny(User $user): bool
    {
        return $user->can('force_delete_any_drug');
    }

    public function restore(User $user, Drug $drug): bool
    {
        return $user->can('restore_drug');
    }

    public function restoreAny(User $user): bool
    {
        return $user->can('restore_any_drug');
    }

    public function replicate(User $user, Drug $drug): bool
    {
        return $user->can('replicate_drug');
    }

    public function reorder(User $user): bool
    {
        return $user->can('reorder_drug');
    }
}
```

## User Management

### 1. User Resource
```php
// app/Filament/Resources/UserResource.php
class UserResource extends Resource
{
    protected static ?string $model = User::class;
    protected static ?string $navigationIcon = 'heroicon-o-users';
    protected static ?string $navigationGroup = 'User Management';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Section::make('User Information')
                    ->schema([
                        TextInput::make('name')
                            ->required()
                            ->maxLength(255),
                        
                        TextInput::make('email')
                            ->email()
                            ->required()
                            ->unique(ignoreRecord: true)
                            ->maxLength(255),
                        
                        TextInput::make('phone')
                            ->tel()
                            ->maxLength(15),
                        
                        TextInput::make('employee_id')
                            ->unique(ignoreRecord: true)
                            ->maxLength(20),
                    ])->columns(2),

                Section::make('Job Information')
                    ->schema([
                        TextInput::make('department')
                            ->maxLength(100),
                        
                        TextInput::make('position')
                            ->maxLength(100),
                        
                        Toggle::make('is_active')
                            ->default(true),
                    ])->columns(2),

                Section::make('Security')
                    ->schema([
                        TextInput::make('password')
                            ->password()
                            ->required(fn (string $context): bool => $context === 'create')
                            ->minLength(8)
                            ->same('password_confirmation')
                            ->dehydrated(fn ($state) => filled($state))
                            ->dehydrateStateUsing(fn ($state) => Hash::make($state)),
                        
                        TextInput::make('password_confirmation')
                            ->password()
                            ->required(fn (string $context): bool => $context === 'create')
                            ->minLength(8)
                            ->dehydrated(false),
                    ])->columns(2)
                    ->visibleOn('create'),

                Section::make('Roles & Permissions')
                    ->schema([
                        CheckboxList::make('roles')
                            ->relationship('roles', 'name')
                            ->getOptionLabelFromRecordUsing(fn (Role $record) => $record->display_name ?? $record->name)
                            ->columns(2),
                    ]),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                ImageColumn::make('avatar_url')
                    ->circular()
                    ->defaultImageUrl(fn ($record) => 
                        'https://ui-avatars.com/api/?name=' . urlencode($record->name)
                    ),
                
                TextColumn::make('name')
                    ->searchable()
                    ->sortable(),
                
                TextColumn::make('email')
                    ->searchable()
                    ->sortable()
                    ->copyable(),
                
                TextColumn::make('employee_id')
                    ->searchable(),
                
                TextColumn::make('roles.name')
                    ->badge()
                    ->separator(','),
                
                IconColumn::make('is_active')
                    ->boolean(),
                
                TextColumn::make('last_login_at')
                    ->dateTime()
                    ->sortable()
                    ->placeholder('Never'),
                
                TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                SelectFilter::make('roles')
                    ->relationship('roles', 'name')
                    ->multiple()
                    ->preload(),
                
                TernaryFilter::make('is_active')
                    ->label('Active Status'),
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make(),
                Tables\Actions\Action::make('impersonate')
                    ->icon('heroicon-o-user-circle')
                    ->action(fn (User $record) => 
                        redirect()->route('filament.admin.auth.impersonate', $record)
                    )
                    ->visible(fn () => auth()->user()->can('impersonate_users')),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                    Tables\Actions\BulkAction::make('activate')
                        ->action(fn (Collection $records) => 
                            $records->each->update(['is_active' => true])
                        )
                        ->requiresConfirmation()
                        ->icon('heroicon-o-check-circle'),
                ]),
            ]);
    }
}
```

### 2. Role Resource (Extended)
```php
// app/Filament/Resources/RoleResource.php
class RoleResource extends Resource
{
    protected static ?string $model = Role::class;
    protected static ?string $navigationIcon = 'heroicon-o-shield-check';
    protected static ?string $navigationGroup = 'User Management';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                TextInput::make('name')
                    ->required()
                    ->unique(ignoreRecord: true)
                    ->maxLength(255),
                
                TextInput::make('display_name')
                    ->maxLength(255),
                
                Textarea::make('description')
                    ->maxLength(500),
                
                CheckboxList::make('permissions')
                    ->relationship('permissions', 'name')
                    ->getOptionLabelFromRecordUsing(fn (Permission $record) => 
                        str_replace('_', ' ', title_case($record->name))
                    )
                    ->columns(3)
                    ->searchable(),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('display_name')
                    ->label('Role Name')
                    ->searchable()
                    ->sortable(),
                
                TextColumn::make('name')
                    ->label('System Name')
                    ->searchable()
                    ->fontFamily(FontFamily::Mono),
                
                TextColumn::make('users_count')
                    ->counts('users')
                    ->label('Users'),
                
                TextColumn::make('permissions_count')
                    ->counts('permissions')
                    ->label('Permissions'),
                
                TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make()
                    ->visible(fn (Role $record) => !in_array($record->name, [
                        'pharmacy_owner', 'super_admin'
                    ])),
            ]);
    }
}
```

## Security Features

### 1. Audit Logging
```php
// app/Models/AuditLog.php
class AuditLog extends Model
{
    protected $fillable = [
        'user_id', 'action', 'model_type', 'model_id',
        'old_values', 'new_values', 'ip_address', 'user_agent'
    ];

    protected $casts = [
        'old_values' => 'array',
        'new_values' => 'array',
    ];

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function model()
    {
        return $this->morphTo();
    }
}

// app/Observers/AuditObserver.php
class AuditObserver
{
    public function created(Model $model): void
    {
        $this->logActivity('created', $model, [], $model->toArray());
    }

    public function updated(Model $model): void
    {
        $this->logActivity('updated', $model, $model->getOriginal(), $model->getChanges());
    }

    public function deleted(Model $model): void
    {
        $this->logActivity('deleted', $model, $model->toArray(), []);
    }

    private function logActivity(string $action, Model $model, array $oldValues, array $newValues): void
    {
        if (!auth()->check()) return;

        AuditLog::create([
            'user_id' => auth()->id(),
            'action' => $action,
            'model_type' => get_class($model),
            'model_id' => $model->getKey(),
            'old_values' => $oldValues,
            'new_values' => $newValues,
            'ip_address' => request()->ip(),
            'user_agent' => request()->userAgent(),
        ]);
    }
}
```

### 2. Login Security
```php
// app/Http/Middleware/TrackLastLogin.php
class TrackLastLogin
{
    public function handle(Request $request, Closure $next)
    {
        if (auth()->check() && !session()->has('last_login_tracked')) {
            auth()->user()->update(['last_login_at' => now()]);
            session()->put('last_login_tracked', true);
        }

        return $next($request);
    }
}

// app/Listeners/LoginListener.php
class LoginListener
{
    public function handle(Login $event): void
    {
        AuditLog::create([
            'user_id' => $event->user->id,
            'action' => 'login',
            'model_type' => User::class,
            'model_id' => $event->user->id,
            'ip_address' => request()->ip(),
            'user_agent' => request()->userAgent(),
        ]);
    }
}
```

## Tenant Isolation

### 1. Tenant Middleware
```php
// app/Http/Middleware/EnsureTenantAccess.php
class EnsureTenantAccess
{
    public function handle(Request $request, Closure $next)
    {
        if (!tenant()) {
            abort(404, 'Tenant not found');
        }

        if (!tenant()->isActive()) {
            return response()->view('tenant.suspended', [
                'tenant' => tenant()
            ], 403);
        }

        // Ensure user belongs to current tenant
        if (auth()->check()) {
            $user = auth()->user();
            
            // Check if user has access to this tenant
            if (!$this->userHasTenantAccess($user, tenant())) {
                auth()->logout();
                return redirect()->route('filament.admin.auth.login')
                    ->with('error', 'Access denied to this pharmacy.');
            }
        }

        return $next($request);
    }

    private function userHasTenantAccess(User $user, $tenant): bool
    {
        // Super admin can access any tenant
        if ($user->hasRole('super_admin')) {
            return true;
        }

        // Check if user was created in this tenant context
        return $user->created_at >= $tenant->created_at;
    }
}
```

## Câu lệnh để bắt đầu
"Hãy thiết lập Filament authentication với multi-tenant RBAC, bao gồm User Resource, Role management và audit logging cho SaaS hiệu thuốc"
