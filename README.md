# 🏥 <PERSON>ệ thống Quản lý <PERSON>án hàng Hiệu thuốc (PMQL)

Dự án <PERSON> với hệ thống AI Agents hỗ trợ phát triển

## 🤖 AI Agents được tích hợp

- **BackendAgent**: X<PERSON>y dựng logic nghiệp vụ <PERSON>
- **InvoiceAgent**: Xử lý hóa đơn và thuế
- **DrugAgent**: Quản lý dược phẩm
- **CustomerAgent**: Quản lý khách hàng
- **AuthAgent**: Phân quyền và bảo mật
- **TestAgent**: Unit & Feature Tests
- **DocAgent**: Tạo tài liệu API

## 📁 Cấu trúc dự án

```
/agents-prompts/     # AI Agent prompts
/app/               # Laravel application
/database/          # Migrations & seeders
/tests/             # PHPUnit tests
```

## 🚀 Cách sử dụng AI Agents

1. Mở VSCode với Augment Code extension
2. Chọn file prompt tương ứng từ `/agents-prompts/`
3. Copy prompt và sử dụng với Augment Agent
4. Phát triển từng module một cách có hệ thống

## 📋 Yêu cầu hệ thống

- PHP 8.1+
- Laravel 10+
- MySQL 8.0+
- Composer

## ⚡ Bắt đầu nhanh

```bash
# Tạo Laravel project
composer create-project laravel/laravel .

# Cài đặt dependencies
composer install

# Cấu hình database
cp .env.example .env
php artisan key:generate

# Chạy migrations
php artisan migrate

# Khởi động server
php artisan serve
```

## 🏗️ Quy trình phát triển

1. **Thiết kế database**: Sử dụng `DatabaseAgent` để tạo migrations
2. **Xây dựng models**: Dùng `BackendAgent` cho Eloquent models
3. **Tạo API**: `BackendAgent` + `DocAgent` cho RESTful API
4. **Quản lý thuốc**: `DrugAgent` cho logic dược phẩm
5. **Hệ thống hóa đơn**: `InvoiceAgent` cho xuất hóa đơn
6. **Phân quyền**: `AuthAgent` cho authentication/authorization
7. **Testing**: `TestAgent` cho unit và feature tests

## 📞 Liên hệ

Dự án được phát triển với sự hỗ trợ của Augment Code AI Agents
