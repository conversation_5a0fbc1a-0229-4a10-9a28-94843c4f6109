# 🏥 SaaS Pharmacy Management System (PMQL)

Hệ thống SaaS multi-tenant quản lý hiệu thuốc vớ<PERSON> 11 + Filament 3

## 🏗️ Architecture Overview

**Multi-tenant SaaS Platform** cung cấp dịch vụ quản lý hiệu thuốc:
- **Platform Provider** (Super Admin) → **Pharmacy Owners** → **Staff** + **Customer Portal**
- **Laravel 11** + **Filament 3** + **Blade** + **Livewire**
- **Flutter Mobile App** + **Public Frontend** + **Customer Portal**

## 🤖 AI Agents Collection

| Agent | Mô tả | Tech Stack |
|-------|-------|------------|
| 🏢 **TenantAgent** | Multi-tenancy setup | Laravel Tenancy + Database per tenant |
| 🎨 **FilamentAgent** | Admin panel resources | Filament 3 Resources + Pages + Widgets |
| 🔐 **AuthAgent** | Authentication & roles | Filament Shield + Spatie Permission |
| 💊 **DrugAgent** | Inventory management | Filament forms + FEFO logic |
| 🧾 **InvoiceAgent** | Sales & invoicing | Filament + PDF + Tax calculation |
| 👥 **CustomerAgent** | Customer management | Filament + Customer portal |
| 📱 **ApiAgent** | Mobile API | Laravel API + Sanctum |
| 🌐 **PublicAgent** | Marketing website | Laravel Blade + Tailwind |
| 🧪 **TestAgent** | Testing suite | Livewire tests + API tests |
| 📖 **DocAgent** | Documentation | Multi-interface docs |

## 🎭 User Hierarchy

```
🏢 Super Admin (Platform Owner)
├── Manage all tenants
├── System analytics
├── Billing & subscriptions
└── Platform settings

🏪 Pharmacy Owner (Tenant Admin)
├── Manage pharmacy
├── Add/remove staff
├── View reports
└── Subscription management

👨‍⚕️ Pharmacist → 💰 Cashier → 📦 Warehouse Staff → 📊 Manager
```

## 📁 Project Structure

```
/agents-prompts/     # AI Agent prompts (SaaS multi-tenant)
/app/
├── Filament/        # Filament resources & pages
├── Models/          # Multi-tenant models
├── Services/        # Business logic
└── Http/Api/        # Mobile API
/public-frontend/    # Marketing website
/customer-portal/    # Invoice viewing
/database/
├── migrations/      # Multi-tenant migrations
└── seeders/         # Tenant seeders
```

## 🚀 Tech Requirements

- **PHP 8.2+**
- **Laravel 11**
- **Filament 3**
- **MySQL 8.0**
- **Redis** (cache + queues)
- **Node.js 18+** (for assets)

## ⚡ Quick Start

```bash
# Create Laravel 11 project
composer create-project laravel/laravel:^11.0 pharmacy-saas
cd pharmacy-saas

# Install Filament 3
composer require filament/filament:"^3.0"
php artisan filament:install --panels

# Install multi-tenancy
composer require stancl/tenancy

# Install additional packages
composer require spatie/laravel-permission
composer require laravel/sanctum
```

## 🎯 Development Roadmap

**Phase 1**: Multi-tenancy + Filament setup
**Phase 2**: Core pharmacy features
**Phase 3**: Mobile API + Public frontend
**Phase 4**: Testing + Documentation

---

**Powered by AI Agents for rapid SaaS development! 🚀**
