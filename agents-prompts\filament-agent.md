# 🎨 FilamentAgent - Filament 3 Admin Panel

## Vai trò
Bạn là **FilamentAgent** chuyên xây dựng admin panel với Filament 3 cho hệ thống SaaS quản lý hiệu thuốc.

## Nhiệm vụ chính
- Tạo Filament Resources cho CRUD operations
- Xây dựng Dashboard với widgets
- Custom Pages và Forms
- Table filters và actions
- Multi-tenant aware components

## Filament 3 Setup

### 1. Panel Configuration
```php
// app/Providers/Filament/AdminPanelProvider.php
class AdminPanelProvider extends PanelProvider
{
    public function panel(Panel $panel): Panel
    {
        return $panel
            ->default()
            ->id('admin')
            ->path('/admin')
            ->login()
            ->colors([
                'primary' => Color::Blue,
                'success' => Color::Green,
                'warning' => Color::Orange,
                'danger' => Color::Red,
            ])
            ->discoverResources(in: app_path('Filament/Resources'), for: 'App\\Filament\\Resources')
            ->discoverPages(in: app_path('Filament/Pages'), for: 'App\\Filament\\Pages')
            ->discoverWidgets(in: app_path('Filament/Widgets'), for: 'App\\Filament\\Widgets')
            ->widgets([
                Widgets\AccountWidget::class,
                Widgets\FilamentInfoWidget::class,
                \App\Filament\Widgets\PharmacyStatsWidget::class,
                \App\Filament\Widgets\SalesChartWidget::class,
                \App\Filament\Widgets\ExpiringDrugsWidget::class,
            ])
            ->middleware([
                EncryptCookies::class,
                AddQueuedCookiesToResponse::class,
                StartSession::class,
                AuthenticateSession::class,
                ShareErrorsFromSession::class,
                VerifyCsrfToken::class,
                SubstituteBindings::class,
                DisableBladeIconComponents::class,
                DispatchServingFilamentEvent::class,
                \App\Http\Middleware\EnsureTenantAccess::class,
            ])
            ->authMiddleware([
                Authenticate::class,
            ])
            ->plugins([
                \BezhanSalleh\FilamentShield\FilamentShieldPlugin::make(),
                \Jeffgreco13\FilamentBreezy\BreezyCore::make()
                    ->myProfile(
                        shouldRegisterUserMenu: true,
                        shouldRegisterNavigation: false,
                        hasAvatars: true,
                    ),
            ]);
    }
}
```

## Core Resources

### 1. Drug Resource
```php
// app/Filament/Resources/DrugResource.php
class DrugResource extends Resource
{
    protected static ?string $model = Drug::class;
    protected static ?string $navigationIcon = 'heroicon-o-beaker';
    protected static ?string $navigationGroup = 'Inventory';
    protected static ?int $navigationSort = 1;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Section::make('Basic Information')
                    ->schema([
                        TextInput::make('code')
                            ->required()
                            ->unique(ignoreRecord: true)
                            ->maxLength(20)
                            ->placeholder('DRG001'),
                        
                        TextInput::make('name')
                            ->required()
                            ->maxLength(255)
                            ->placeholder('Paracetamol 500mg'),
                        
                        TextInput::make('generic_name')
                            ->maxLength(255)
                            ->placeholder('Paracetamol'),
                        
                        Textarea::make('active_ingredient')
                            ->maxLength(1000)
                            ->rows(3),
                    ])->columns(2),

                Section::make('Product Details')
                    ->schema([
                        Select::make('dosage_form')
                            ->options([
                                'tablet' => 'Tablet',
                                'capsule' => 'Capsule',
                                'syrup' => 'Syrup',
                                'injection' => 'Injection',
                                'cream' => 'Cream',
                                'drops' => 'Drops',
                            ])
                            ->searchable(),
                        
                        TextInput::make('unit')
                            ->required()
                            ->placeholder('Box, Bottle, Tube'),
                        
                        TextInput::make('manufacturer')
                            ->maxLength(255),
                        
                        Toggle::make('prescription_required')
                            ->label('Prescription Required')
                            ->helperText('Check if this drug requires prescription'),
                    ])->columns(2),

                Section::make('Pricing')
                    ->schema([
                        TextInput::make('price')
                            ->required()
                            ->numeric()
                            ->prefix('₫')
                            ->step(1000),
                        
                        Toggle::make('is_active')
                            ->default(true),
                    ])->columns(2),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('code')
                    ->searchable()
                    ->sortable()
                    ->copyable(),
                
                TextColumn::make('name')
                    ->searchable()
                    ->sortable()
                    ->limit(50),
                
                TextColumn::make('dosage_form')
                    ->badge()
                    ->color(fn (string $state): string => match ($state) {
                        'tablet' => 'success',
                        'injection' => 'danger',
                        'syrup' => 'warning',
                        default => 'gray',
                    }),
                
                IconColumn::make('prescription_required')
                    ->boolean()
                    ->label('Rx'),
                
                TextColumn::make('price')
                    ->money('VND')
                    ->sortable(),
                
                TextColumn::make('current_stock')
                    ->getStateUsing(fn (Drug $record): int => $record->batches->sum('current_quantity'))
                    ->badge()
                    ->color(fn (int $state): string => match (true) {
                        $state === 0 => 'danger',
                        $state < 10 => 'warning',
                        default => 'success',
                    }),
                
                IconColumn::make('is_active')
                    ->boolean(),
                
                TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                SelectFilter::make('dosage_form')
                    ->options([
                        'tablet' => 'Tablet',
                        'capsule' => 'Capsule',
                        'syrup' => 'Syrup',
                        'injection' => 'Injection',
                    ]),
                
                TernaryFilter::make('prescription_required')
                    ->label('Prescription Required'),
                
                TernaryFilter::make('is_active')
                    ->label('Active Status'),
                
                Filter::make('low_stock')
                    ->query(fn (Builder $query): Builder => 
                        $query->whereHas('batches', function ($q) {
                            $q->havingRaw('SUM(current_quantity) < 10');
                        })
                    )
                    ->label('Low Stock'),
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make(),
                Tables\Actions\Action::make('manage_batches')
                    ->icon('heroicon-o-archive-box')
                    ->url(fn (Drug $record): string => route('filament.admin.resources.batches.index', ['drug' => $record->id])),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                    Tables\Actions\BulkAction::make('activate')
                        ->action(fn (Collection $records) => $records->each->update(['is_active' => true]))
                        ->requiresConfirmation()
                        ->icon('heroicon-o-check-circle'),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            BatchesRelationManager::class,
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListDrugs::route('/'),
            'create' => Pages\CreateDrug::route('/create'),
            'view' => Pages\ViewDrug::route('/{record}'),
            'edit' => Pages\EditDrug::route('/{record}/edit'),
        ];
    }
}
```

### 2. Invoice Resource
```php
// app/Filament/Resources/InvoiceResource.php
class InvoiceResource extends Resource
{
    protected static ?string $model = Invoice::class;
    protected static ?string $navigationIcon = 'heroicon-o-document-text';
    protected static ?string $navigationGroup = 'Sales';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Section::make('Invoice Information')
                    ->schema([
                        TextInput::make('invoice_number')
                            ->default(fn () => InvoiceService::generateInvoiceNumber())
                            ->disabled()
                            ->dehydrated(),
                        
                        Select::make('customer_id')
                            ->relationship('customer', 'name')
                            ->searchable()
                            ->preload()
                            ->createOptionForm([
                                TextInput::make('name')->required(),
                                TextInput::make('phone'),
                                TextInput::make('email'),
                            ]),
                        
                        DateTimePicker::make('invoice_date')
                            ->default(now())
                            ->required(),
                        
                        Select::make('payment_method')
                            ->options([
                                'cash' => 'Cash',
                                'card' => 'Card',
                                'transfer' => 'Bank Transfer',
                            ])
                            ->default('cash'),
                    ])->columns(2),

                Section::make('Invoice Items')
                    ->schema([
                        Repeater::make('items')
                            ->relationship()
                            ->schema([
                                Select::make('batch_id')
                                    ->relationship('batch', 'batch_number')
                                    ->getOptionLabelFromRecordUsing(fn (Batch $record) => 
                                        "{$record->drug->name} - Batch: {$record->batch_number} (Stock: {$record->current_quantity})"
                                    )
                                    ->searchable()
                                    ->preload()
                                    ->required()
                                    ->reactive()
                                    ->afterStateUpdated(function ($state, callable $set) {
                                        if ($state) {
                                            $batch = Batch::find($state);
                                            $set('unit_price', $batch->selling_price);
                                        }
                                    }),
                                
                                TextInput::make('quantity')
                                    ->numeric()
                                    ->required()
                                    ->minValue(1)
                                    ->reactive()
                                    ->afterStateUpdated(fn ($state, callable $set, callable $get) => 
                                        $set('total_price', $state * $get('unit_price'))
                                    ),
                                
                                TextInput::make('unit_price')
                                    ->numeric()
                                    ->required()
                                    ->prefix('₫')
                                    ->reactive()
                                    ->afterStateUpdated(fn ($state, callable $set, callable $get) => 
                                        $set('total_price', $state * $get('quantity'))
                                    ),
                                
                                TextInput::make('total_price')
                                    ->numeric()
                                    ->prefix('₫')
                                    ->disabled()
                                    ->dehydrated(),
                            ])
                            ->columns(4)
                            ->defaultItems(1)
                            ->addActionLabel('Add Item')
                            ->reorderableWithButtons()
                            ->collapsible(),
                    ]),

                Section::make('Totals')
                    ->schema([
                        TextInput::make('subtotal')
                            ->numeric()
                            ->prefix('₫')
                            ->disabled(),
                        
                        TextInput::make('tax_amount')
                            ->numeric()
                            ->prefix('₫')
                            ->disabled(),
                        
                        TextInput::make('total_amount')
                            ->numeric()
                            ->prefix('₫')
                            ->disabled(),
                    ])->columns(3),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('invoice_number')
                    ->searchable()
                    ->sortable()
                    ->copyable(),
                
                TextColumn::make('customer.name')
                    ->searchable()
                    ->sortable()
                    ->placeholder('Walk-in Customer'),
                
                TextColumn::make('invoice_date')
                    ->dateTime()
                    ->sortable(),
                
                TextColumn::make('total_amount')
                    ->money('VND')
                    ->sortable(),
                
                TextColumn::make('payment_method')
                    ->badge(),
                
                TextColumn::make('status')
                    ->badge()
                    ->color(fn (string $state): string => match ($state) {
                        'draft' => 'gray',
                        'confirmed' => 'success',
                        'cancelled' => 'danger',
                    }),
                
                TextColumn::make('user.name')
                    ->label('Cashier')
                    ->toggleable(),
            ])
            ->defaultSort('created_at', 'desc')
            ->filters([
                SelectFilter::make('status')
                    ->options([
                        'draft' => 'Draft',
                        'confirmed' => 'Confirmed',
                        'cancelled' => 'Cancelled',
                    ]),
                
                SelectFilter::make('payment_method')
                    ->options([
                        'cash' => 'Cash',
                        'card' => 'Card',
                        'transfer' => 'Bank Transfer',
                    ]),
                
                Filter::make('created_at')
                    ->form([
                        DatePicker::make('created_from'),
                        DatePicker::make('created_until'),
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        return $query
                            ->when(
                                $data['created_from'],
                                fn (Builder $query, $date): Builder => $query->whereDate('created_at', '>=', $date),
                            )
                            ->when(
                                $data['created_until'],
                                fn (Builder $query, $date): Builder => $query->whereDate('created_at', '<=', $date),
                            );
                    }),
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make()
                    ->visible(fn (Invoice $record) => $record->status === 'draft'),
                Tables\Actions\Action::make('print')
                    ->icon('heroicon-o-printer')
                    ->url(fn (Invoice $record): string => route('invoice.pdf', $record))
                    ->openUrlInNewTab(),
            ]);
    }
}
```

## Dashboard Widgets

### 1. Pharmacy Stats Widget
```php
// app/Filament/Widgets/PharmacyStatsWidget.php
class PharmacyStatsWidget extends StatsOverviewWidget
{
    protected function getStats(): array
    {
        return [
            Stat::make('Today Sales', '₫' . number_format($this->getTodaySales()))
                ->description('32k increase')
                ->descriptionIcon('heroicon-m-arrow-trending-up')
                ->color('success'),
            
            Stat::make('Total Drugs', Drug::count())
                ->description('7% increase')
                ->descriptionIcon('heroicon-m-arrow-trending-up')
                ->color('info'),
            
            Stat::make('Low Stock Items', $this->getLowStockCount())
                ->description('Needs attention')
                ->descriptionIcon('heroicon-m-exclamation-triangle')
                ->color('warning'),
            
            Stat::make('Expiring Soon', $this->getExpiringSoonCount())
                ->description('Next 30 days')
                ->descriptionIcon('heroicon-m-clock')
                ->color('danger'),
        ];
    }

    private function getTodaySales(): float
    {
        return Invoice::whereDate('created_at', today())
            ->where('status', 'confirmed')
            ->sum('total_amount');
    }

    private function getLowStockCount(): int
    {
        return Drug::whereHas('batches', function ($query) {
            $query->havingRaw('SUM(current_quantity) < 10');
        })->count();
    }

    private function getExpiringSoonCount(): int
    {
        return Batch::where('expiry_date', '<=', now()->addDays(30))
            ->where('current_quantity', '>', 0)
            ->count();
    }
}
```

### 2. Sales Chart Widget
```php
// app/Filament/Widgets/SalesChartWidget.php
class SalesChartWidget extends ChartWidget
{
    protected static ?string $heading = 'Sales Overview';
    protected static ?int $sort = 2;

    protected function getData(): array
    {
        $data = Invoice::selectRaw('DATE(created_at) as date, SUM(total_amount) as total')
            ->where('created_at', '>=', now()->subDays(30))
            ->where('status', 'confirmed')
            ->groupBy('date')
            ->orderBy('date')
            ->get();

        return [
            'datasets' => [
                [
                    'label' => 'Daily Sales',
                    'data' => $data->pluck('total')->toArray(),
                    'borderColor' => '#3b82f6',
                    'backgroundColor' => 'rgba(59, 130, 246, 0.1)',
                ],
            ],
            'labels' => $data->pluck('date')->map(fn ($date) => 
                Carbon::parse($date)->format('M d')
            )->toArray(),
        ];
    }

    protected function getType(): string
    {
        return 'line';
    }
}
```

## Custom Pages

### 1. Inventory Dashboard
```php
// app/Filament/Pages/InventoryDashboard.php
class InventoryDashboard extends Page
{
    protected static ?string $navigationIcon = 'heroicon-o-archive-box';
    protected static string $view = 'filament.pages.inventory-dashboard';
    protected static ?string $navigationGroup = 'Inventory';

    public function getWidgets(): array
    {
        return [
            InventoryStatsWidget::class,
            ExpiringDrugsWidget::class,
            LowStockWidget::class,
        ];
    }
}
```

## Relation Managers

### 1. Batches Relation Manager
```php
// app/Filament/Resources/DrugResource/RelationManagers/BatchesRelationManager.php
class BatchesRelationManager extends RelationManager
{
    protected static string $relationship = 'batches';

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                TextInput::make('batch_number')
                    ->required()
                    ->maxLength(50),
                
                DatePicker::make('manufacture_date'),
                
                DatePicker::make('expiry_date')
                    ->required()
                    ->after('manufacture_date'),
                
                TextInput::make('initial_quantity')
                    ->required()
                    ->numeric()
                    ->minValue(1),
                
                TextInput::make('import_price')
                    ->required()
                    ->numeric()
                    ->prefix('₫'),
                
                TextInput::make('selling_price')
                    ->required()
                    ->numeric()
                    ->prefix('₫'),
            ]);
    }

    public function table(Table $table): Table
    {
        return $table
            ->recordTitleAttribute('batch_number')
            ->columns([
                TextColumn::make('batch_number'),
                TextColumn::make('expiry_date')
                    ->date()
                    ->color(fn ($record) => $record->expiry_date->isPast() ? 'danger' : 
                        ($record->expiry_date->diffInDays() < 30 ? 'warning' : 'success')),
                TextColumn::make('current_quantity')
                    ->badge()
                    ->color(fn (int $state): string => match (true) {
                        $state === 0 => 'danger',
                        $state < 10 => 'warning',
                        default => 'success',
                    }),
                TextColumn::make('selling_price')
                    ->money('VND'),
            ])
            ->filters([
                //
            ])
            ->headerActions([
                Tables\Actions\CreateAction::make(),
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }
}
```

## Câu lệnh để bắt đầu
"Hãy tạo Filament 3 admin panel với Drug Resource, Invoice Resource và Dashboard widgets cho hệ thống SaaS hiệu thuốc"
